<?php

/**
 * Debug script để kiểm tra tại sao ngày 28/6/2025 của user 1814 có checkout 17:32
 * 
 * Chạy: php debug_checkout_time.php
 */

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== DEBUG: CHECKOUT TIME FOR 28/6/2025 USER 1814 ===\n\n";

try {
    $userId = 1814;
    $date = '2025-06-28';
    
    echo "Checking data for User ID: {$userId}, Date: {$date}\n\n";
    
    // 1. Ki<PERSON><PERSON> tra timesheets
    echo "1. TIMESHEETS DATA:\n";
    $timesheets = App\Models\Timesheet::where('user_id', $userId)
        ->where('date', $date)
        ->with(['shift', 'shiftRuleHistory'])
        ->get();
    
    echo "Found " . $timesheets->count() . " timesheet(s)\n\n";
    
    foreach ($timesheets as $timesheet) {
        echo "Timesheet ID: {$timesheet->id}\n";
        echo "Date: {$timesheet->date}\n";
        echo "Checkin: " . ($timesheet->checkin ? $timesheet->checkin->format('Y-m-d H:i:s') : 'null') . "\n";
        echo "Checkout: " . ($timesheet->checkout ? $timesheet->checkout->format('Y-m-d H:i:s') : 'null') . "\n";
        echo "Shift ID: {$timesheet->shift_id}\n";
        echo "Shift Rule History ID: " . ($timesheet->shift_rule_history_id ?? 'null') . "\n";
        echo "Workday: {$timesheet->workday}\n";
        
        if ($timesheet->shift) {
            echo "Shift Name: {$timesheet->shift->name}\n";
            echo "Shift Original Time: {$timesheet->shift->start_time} - {$timesheet->shift->end_time}\n";
        }
        
        if ($timesheet->shiftRuleHistory) {
            echo "Shift Rule Time: {$timesheet->shiftRuleHistory->start_time} - {$timesheet->shiftRuleHistory->end_time}\n";
            echo "Rule Effective: {$timesheet->shiftRuleHistory->effective_from} to " . ($timesheet->shiftRuleHistory->effective_to ?? 'null') . "\n";
        }
        
        // Test getShiftRule method
        $shiftRule = $timesheet->getShiftRule();
        if ($shiftRule) {
            echo "Active Shift Rule: {$shiftRule->start_time} - {$shiftRule->end_time}\n";
        } else {
            echo "Active Shift Rule: null\n";
        }
        
        echo "\n";
    }
    
    // 2. Kiểm tra attendance explanations
    echo "2. ATTENDANCE EXPLANATIONS:\n";
    $explanations = App\Models\AttendanceExplanation::where('user_id', $userId)
        ->where('date', $date)
        ->with(['remoteShift', 'shiftChange'])
        ->get();
    
    echo "Found " . $explanations->count() . " explanation(s)\n\n";
    
    foreach ($explanations as $explanation) {
        echo "Explanation ID: {$explanation->id}\n";
        echo "Type: {$explanation->explanation_type}\n";
        echo "Status: {$explanation->final_status}\n";
        echo "Content: {$explanation->explanation}\n";
        
        if ($explanation->explanation_type === 'shift_change' && $explanation->shiftChange) {
            echo "New Shift: {$explanation->shiftChange->name} ({$explanation->shiftChange->start_time} - {$explanation->shiftChange->end_time})\n";
            
            // Kiểm tra shift rule cho ca mới
            $newShiftRule = $explanation->shiftChange->getActiveRule($date);
            if ($newShiftRule) {
                echo "New Shift Rule: {$newShiftRule->start_time} - {$newShiftRule->end_time}\n";
            }
        }
        
        if ($explanation->explanation_type === 'remote_work' && $explanation->remoteShift) {
            echo "Remote Shift: {$explanation->remoteShift->name} ({$explanation->remoteShift->start_time} - {$explanation->remoteShift->end_time})\n";
        }
        
        if ($explanation->ot_hours) {
            echo "OT Hours: {$explanation->ot_hours}\n";
        }
        
        echo "Created: {$explanation->created_at}\n";
        echo "Updated: {$explanation->updated_at}\n";
        echo "\n";
    }
    
    // 3. Kiểm tra shift rule histories cho user
    echo "3. USER'S SHIFT RULES:\n";
    $user = App\Models\User::find($userId);
    if ($user) {
        $shifts = $user->getAvailableShifts();
        foreach ($shifts as $shift) {
            echo "Shift: {$shift->name} (ID: {$shift->id})\n";
            echo "Original: {$shift->start_time} - {$shift->end_time}\n";
            
            $activeRule = $shift->getActiveRule($date);
            if ($activeRule) {
                echo "Rule for {$date}: {$activeRule->start_time} - {$activeRule->end_time}\n";
                echo "Rule period: {$activeRule->effective_from} to " . ($activeRule->effective_to ?? 'null') . "\n";
            } else {
                echo "No rule for {$date}\n";
            }
            echo "\n";
        }
    }
    
    // 4. Kiểm tra logic tính toán workday
    echo "4. WORKDAY CALCULATION:\n";
    foreach ($timesheets as $timesheet) {
        echo "Timesheet ID: {$timesheet->id}\n";
        
        $calculatedWorkday = $timesheet->calcWorkday();
        echo "Calculated Workday: {$calculatedWorkday}\n";
        
        $calculatedHours = $timesheet->calcWorkDayHours();
        echo "Calculated Hours: {$calculatedHours}\n";
        
        $realHours = $timesheet->calcWorkDayRealHours();
        echo "Real Hours: {$realHours}\n";
        
        echo "\n";
    }
    
    echo "=== ANALYSIS ===\n";
    echo "Checkout time 17:32 có thể đến từ:\n";
    echo "1. Dữ liệu thực tế trong timesheets table (checkin/checkout)\n";
    echo "2. Logic tính toán từ shift change explanation\n";
    echo "3. Adjustment từ attendance explanation được approve\n";
    echo "4. Logic ShiftDeterminationService\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

?>
