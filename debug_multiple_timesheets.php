<?php

/**
 * Debug script để kiểm tra logic getLatestValidCheckout với multiple timesheets
 * 
 * Chạy: php debug_multiple_timesheets.php
 */

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== DEBUG: MULTIPLE TIMESHEETS LOGIC ===\n\n";

// Simulate data cho ngày 28/6/2025 với 2 bản ghi
$timesheets = collect([
    (object) [
        'id' => 1,
        'date' => '2025-06-28',
        'checkin' => \Carbon\Carbon::parse('2025-06-28 08:30:00'),
        'checkout' => \Carbon\Carbon::parse('2025-06-28 17:32:00'),
        'shift_id' => 1,
    ],
    (object) [
        'id' => 2,
        'date' => '2025-06-28',
        'checkin' => \Carbon\Carbon::parse('2025-06-28 18:30:00'),
        'checkout' => \Carbon\Carbon::parse('2025-06-28 20:15:00'), // Giả sử có checkout
        'shift_id' => 1,
    ]
]);

echo "Input data:\n";
foreach ($timesheets as $timesheet) {
    echo "- ID: {$timesheet->id}\n";
    echo "  Checkin: " . $timesheet->checkin->format('Y-m-d H:i:s') . "\n";
    echo "  Checkout: " . ($timesheet->checkout ? $timesheet->checkout->format('Y-m-d H:i:s') : 'null') . "\n";
    echo "\n";
}

// Test NEW logic getLatestValidCheckout
echo "=== Testing NEW getLatestValidCheckout Logic ===\n\n";

function testGetLatestValidCheckout($timesheets, $testName) {
    echo "--- {$testName} ---\n";

    // Sắp xếp theo thời gian checkin để lấy bản ghi cuối cùng
    $sortedTimesheets = $timesheets->sortBy('checkin');
    $lastTimesheet = $sortedTimesheets->last();

    echo "Last timesheet (by checkin):\n";
    echo "- Checkin: " . $lastTimesheet->checkin->format('H:i:s') . "\n";
    echo "- Checkout: " . ($lastTimesheet->checkout ? $lastTimesheet->checkout->format('H:i:s') : 'null') . "\n";

    if ($lastTimesheet) {
        // Case 1: Bản ghi cuối có checkout → Lấy checkout của bản ghi cuối
        if (isset($lastTimesheet->checkout) && $lastTimesheet->checkout) {
            $result = $lastTimesheet->checkout;
            echo "✅ Case 1: Using last timesheet checkout: " . $result->format('H:i:s') . "\n";
            return $result;
        }

        // Case 2: Bản ghi cuối không có checkout → Lấy giờ checkin làm checkout
        if (isset($lastTimesheet->checkin) && $lastTimesheet->checkin) {
            $result = $lastTimesheet->checkin;
            echo "✅ Case 2: Using last timesheet checkin as checkout: " . $result->format('H:i:s') . "\n";
            return $result;
        }
    }

    // Case 3: Fallback
    $timesheetsWithCheckout = $timesheets->filter(function($timesheet) {
        return isset($timesheet->checkout) && $timesheet->checkout !== null;
    });

    if ($timesheetsWithCheckout->isNotEmpty()) {
        $result = $timesheetsWithCheckout->max('checkout');
        echo "✅ Case 3: Using max checkout from other records: " . $result->format('H:i:s') . "\n";
        return $result;
    }

    // Nếu không có checkout nào, lấy checkin muộn nhất
    $result = $timesheets->max('checkin');
    echo "✅ Case 4: Using max checkin: " . $result->format('H:i:s') . "\n";
    return $result;
}

// Test case 1: Bản ghi cuối có checkout
echo "\n=== TEST CASES ===\n";
$result1 = testGetLatestValidCheckout($timesheets, "Case 1: Last timesheet has checkout");

// Test case 2: Bản ghi cuối không có checkout
$timesheets2 = collect([
    (object) [
        'id' => 1,
        'checkin' => \Carbon\Carbon::parse('2025-06-28 08:30:00'),
        'checkout' => \Carbon\Carbon::parse('2025-06-28 17:32:00'),
    ],
    (object) [
        'id' => 2,
        'checkin' => \Carbon\Carbon::parse('2025-06-28 18:30:00'),
        'checkout' => null, // Không có checkout
    ]
]);

echo "\n";
$result2 = testGetLatestValidCheckout($timesheets2, "Case 2: Last timesheet has no checkout");

echo "\n=== SUMMARY ===\n";
echo "Case 1 result: " . $result1->format('H:i:s') . " (Expected: 20:15 - checkout của bản ghi cuối)\n";
echo "Case 2 result: " . $result2->format('H:i:s') . " (Expected: 18:30 - checkin của bản ghi cuối làm checkout)\n";

// Test case 2: Bản ghi cuối không có checkout
echo "\n\n=== TEST CASE 2: Last timesheet has no checkout ===\n";

$timesheets2 = collect([
    (object) [
        'id' => 1,
        'checkin' => \Carbon\Carbon::parse('2025-06-28 08:30:00'),
        'checkout' => \Carbon\Carbon::parse('2025-06-28 17:32:00'),
    ],
    (object) [
        'id' => 2,
        'checkin' => \Carbon\Carbon::parse('2025-06-28 18:30:00'),
        'checkout' => null, // Không có checkout
    ]
]);

echo "Input data:\n";
foreach ($timesheets2 as $timesheet) {
    echo "- Checkin: " . $timesheet->checkin->format('H:i:s') . 
         ", Checkout: " . ($timesheet->checkout ? $timesheet->checkout->format('H:i:s') : 'null') . "\n";
}

$sortedTimesheets2 = $timesheets2->sortBy('checkin');
$lastTimesheet2 = $sortedTimesheets2->last();

echo "\nLast timesheet checkout: " . ($lastTimesheet2->checkout ? $lastTimesheet2->checkout->format('H:i:s') : 'null') . "\n";

if ($lastTimesheet2 && isset($lastTimesheet2->checkout) && $lastTimesheet2->checkout) {
    $result2 = $lastTimesheet2->checkout;
    echo "Using last timesheet: " . $result2->format('H:i:s') . "\n";
} else {
    $result2 = $timesheets2->filter(function($timesheet) {
        return isset($timesheet->checkout) && $timesheet->checkout !== null;
    })->max('checkout');
    echo "Using fallback max: " . ($result2 ? $result2->format('H:i:s') : 'null') . "\n";
}

echo "\n=== ANALYSIS ===\n";
echo "Vấn đề có thể là:\n";
echo "1. Bản ghi checkin 18:30 không có checkout → Fallback về 17:32\n";
echo "2. Logic max('checkout') không hoạt động đúng\n";
echo "3. Data trong database không như mong đợi\n";
echo "4. Logic sắp xếp có vấn đề với Carbon objects\n";

?>
