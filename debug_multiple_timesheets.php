<?php

/**
 * Debug script để kiểm tra logic getLatestValidCheckout với multiple timesheets
 * 
 * Chạy: php debug_multiple_timesheets.php
 */

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== DEBUG: MULTIPLE TIMESHEETS LOGIC ===\n\n";

// Simulate data cho ngày 28/6/2025 với 2 bản ghi
$timesheets = collect([
    (object) [
        'id' => 1,
        'date' => '2025-06-28',
        'checkin' => \Carbon\Carbon::parse('2025-06-28 08:30:00'),
        'checkout' => \Carbon\Carbon::parse('2025-06-28 17:32:00'),
        'shift_id' => 1,
    ],
    (object) [
        'id' => 2,
        'date' => '2025-06-28',
        'checkin' => \Carbon\Carbon::parse('2025-06-28 18:30:00'),
        'checkout' => \Carbon\Carbon::parse('2025-06-28 20:15:00'), // Giả sử có checkout
        'shift_id' => 1,
    ]
]);

echo "Input data:\n";
foreach ($timesheets as $timesheet) {
    echo "- ID: {$timesheet->id}\n";
    echo "  Checkin: " . $timesheet->checkin->format('Y-m-d H:i:s') . "\n";
    echo "  Checkout: " . ($timesheet->checkout ? $timesheet->checkout->format('Y-m-d H:i:s') : 'null') . "\n";
    echo "\n";
}

// Test NEW logic getLatestValidCheckout
echo "=== Testing NEW getLatestValidCheckout Logic ===\n\n";

// NEW Logic: Ưu tiên checkout muộn nhất
echo "1. Filter timesheets with checkout:\n";
$timesheetsWithCheckout = $timesheets->filter(function($timesheet) {
    $checkout = is_object($timesheet) && isset($timesheet->checkout) ? $timesheet->checkout : null;
    return $checkout !== null;
});

foreach ($timesheetsWithCheckout as $timesheet) {
    echo "- ID: {$timesheet->id}, Checkout: " . $timesheet->checkout->format('H:i:s') . "\n";
}

echo "\n2. Get max checkout:\n";
$result = $timesheetsWithCheckout->max('checkout');
echo "Latest checkout: " . ($result ? $result->format('H:i:s') : 'null') . "\n";

echo "\n=== FINAL RESULT ===\n";
echo "Latest valid checkout: " . ($result ? $result->format('H:i:s') : 'null') . "\n";

// Test case 2: Bản ghi cuối không có checkout
echo "\n\n=== TEST CASE 2: Last timesheet has no checkout ===\n";

$timesheets2 = collect([
    (object) [
        'id' => 1,
        'checkin' => \Carbon\Carbon::parse('2025-06-28 08:30:00'),
        'checkout' => \Carbon\Carbon::parse('2025-06-28 17:32:00'),
    ],
    (object) [
        'id' => 2,
        'checkin' => \Carbon\Carbon::parse('2025-06-28 18:30:00'),
        'checkout' => null, // Không có checkout
    ]
]);

echo "Input data:\n";
foreach ($timesheets2 as $timesheet) {
    echo "- Checkin: " . $timesheet->checkin->format('H:i:s') . 
         ", Checkout: " . ($timesheet->checkout ? $timesheet->checkout->format('H:i:s') : 'null') . "\n";
}

$sortedTimesheets2 = $timesheets2->sortBy('checkin');
$lastTimesheet2 = $sortedTimesheets2->last();

echo "\nLast timesheet checkout: " . ($lastTimesheet2->checkout ? $lastTimesheet2->checkout->format('H:i:s') : 'null') . "\n";

if ($lastTimesheet2 && isset($lastTimesheet2->checkout) && $lastTimesheet2->checkout) {
    $result2 = $lastTimesheet2->checkout;
    echo "Using last timesheet: " . $result2->format('H:i:s') . "\n";
} else {
    $result2 = $timesheets2->filter(function($timesheet) {
        return isset($timesheet->checkout) && $timesheet->checkout !== null;
    })->max('checkout');
    echo "Using fallback max: " . ($result2 ? $result2->format('H:i:s') : 'null') . "\n";
}

echo "\n=== ANALYSIS ===\n";
echo "Vấn đề có thể là:\n";
echo "1. Bản ghi checkin 18:30 không có checkout → Fallback về 17:32\n";
echo "2. Logic max('checkout') không hoạt động đúng\n";
echo "3. Data trong database không như mong đợi\n";
echo "4. Logic sắp xếp có vấn đề với Carbon objects\n";

?>
