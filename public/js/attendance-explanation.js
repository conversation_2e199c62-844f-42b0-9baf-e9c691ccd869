/**
 * Attendance Explanation JavaScript
 * Handles all JavaScript functionality for attendance explanation page
 */

$(document).ready(function() {
    // Initialize Select2 for create form
    $('#tagged_user_id').select2({
        placeholder: 'Chọn 1 đồng nghiệp để xác nhận...',
        allowClear: true,
        width: '100%',
        dropdownParent: $('#explanationModal')
    });

    // Initialize Select2 for edit form (will be re-initialized when modal opens)
    $('#edit_tagged_user_id').select2({
        placeholder: 'Chọn 1 đồng nghiệp để xác nhận...',
        allowClear: true,
        width: '100%'
    });

    // Handle click add explanation button
    $('.btn-explain').click(function() {
        var date = $(this).data('date');
        var formattedDate = moment(date).format('DD/MM/YYYY');

        $('#explanation_date').val(date);
        $('#selected_date').text(formattedDate);

        // Store date for shift validation
        window.currentExplanationDate = date;
        console.log('Opening modal for date:', date);

        // Load shifts with rules for this date
        loadShiftsWithRules(date);

        $('#explanationModal').modal('show');
    });

    // Handle explanation type change to show/hide OT hours and remote shift inputs
    $('#explanation_type, #edit_explanation_type').change(function() {
        var selectedType = $(this).val();
        var isEdit = $(this).attr('id').includes('edit_');
        var prefix = isEdit ? 'edit_' : '';

        // Reset all
        $('#' + prefix + 'ot_hours_group').hide();
        $('#' + prefix + 'ot_hours').prop('required', false).val('');
        $('#' + prefix + 'remote_shift_group').hide();
        $('#' + prefix + 'remote_shift_id').prop('required', false).val('');
        $('#' + prefix + 'shift_change_group').hide();
        $('#' + prefix + 'shift_change_id').prop('required', false).val('');

        // Show corresponding input
        if (selectedType === 'overtime') {
            $('#' + prefix + 'ot_hours_group').show();
            $('#' + prefix + 'ot_hours').prop('required', true);
        } else if (selectedType === 'remote_work') {
            $('#' + prefix + 'remote_shift_group').show();
            $('#' + prefix + 'remote_shift_id').prop('required', true);
        } else if (selectedType === 'shift_change') {
            $('#' + prefix + 'shift_change_group').show();
            $('#' + prefix + 'shift_change_id').prop('required', true);

            // Load timesheet data for validation if not edit mode
            if (!isEdit && window.currentExplanationDate) {
                console.log('Loading timesheet data for date:', window.currentExplanationDate);
                loadTimesheetForValidation(window.currentExplanationDate);
            }
        }
    });

    // Handle shift change selection for validation (using event delegation)
    $(document).on('change', '#shift_change_id, #edit_shift_change_id', function() {
        var isEdit = $(this).attr('id').includes('edit_');
        var selectedShiftId = $(this).val();

        console.log('Shift change selection:', {
            isEdit: isEdit,
            selectedShiftId: selectedShiftId,
            hasTimesheetData: !!window.currentTimesheetData
        });

        if (selectedShiftId && window.currentTimesheetData) {
            validateShiftSelection(selectedShiftId, isEdit);
        } else {
            console.log('No validation - missing data');
            hideShiftWarning(isEdit);
        }
    });

    // Handle click view explanations button
    $('.btn-view-explanations').click(function() {
        var date = $(this).data('date');
        var formattedDate = moment(date).format('DD/MM/YYYY');

        console.log('Opening explanations modal for date:', date, 'formatted:', formattedDate);
        $('#view_selected_date').text(formattedDate);
        loadExplanations(date);
        $('#viewExplanationsModal').modal('show');
    });

    // Handle explanation form submit
    $('#explanationForm').submit(function(e) {
        e.preventDefault();

        // Check limit before submit (except OT, remote work, shift_change, new_employee_no_account and other)
        var explanationType = $('#explanation_type').val();
        if (explanationType !== 'overtime' && explanationType !== 'remote_work' && explanationType !== 'shift_change' && explanationType !== 'new_employee_no_account' && explanationType !== 'other') {
            var currentCount = window.monthlyStatsRegularCount || 0;
            if (currentCount >= 3) {
                showToast('error', 'Giới hạn', 'Bạn đã tạo đủ 3 giải trình trong tháng này (không bao gồm OT, remote work và lý do khác). Vui lòng xóa giải trình cũ nếu muốn tạo mới.');
                return;
            }
        }

        // Use FormData to handle form properly
        var formData = new FormData(this);

        // Disable submit button
        var submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true).text('Đang lưu...');

        $.ajax({
            url: window.routes.attendanceExplanationStore,
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    // Close modal
                    $('#explanationModal').modal('hide');

                    // Reset form
                    $('#explanationForm')[0].reset();
                    $('#tagged_user_id').val('').trigger('change');

                    // Show success toast
                    showToast('success', 'Thành công!', response.message || 'Đã lưu giải trình thành công!');

                    // Refresh page to show new explanation
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    showToast('error', 'Lỗi!', response.message || 'Có lỗi xảy ra khi lưu giải trình');
                }
            },
            error: function(xhr) {
                var message = 'Có lỗi xảy ra!';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    var errors = xhr.responseJSON.errors;
                    message = Object.values(errors).flat().join('<br>');
                }
                showToast('error', 'Lỗi!', message);
            },
            complete: function() {
                submitBtn.prop('disabled', false).text('Lưu giải trình');
            }
        });
    });

    // Reset form when closing modal
    $('#explanationModal').on('hidden.bs.modal', function() {
        $('#explanationForm')[0].reset();
        $('#tagged_user_id').val('').trigger('change');
        $('#ot_hours_group').hide();
        $('#ot_hours').prop('required', false);
        $('#remote_shift_group').hide();
        $('#remote_shift_id').prop('required', false);
        $('#shift_change_group').hide();
        $('#shift_change_id').prop('required', false);
        hideShiftWarning(false);
        window.currentTimesheetData = null;
    });

    // Reset edit form when closing modal
    $('#editExplanationModal').on('hidden.bs.modal', function() {
        $('#editExplanationForm')[0].reset();
        $('#edit_tagged_user_id').val('').trigger('change');
        $('#edit_ot_hours_group').hide();
        $('#edit_ot_hours').prop('required', false);
        $('#edit_remote_shift_group').hide();
        $('#edit_remote_shift_id').prop('required', false);
        $('#edit_shift_change_group').hide();
        $('#edit_shift_change_id').prop('required', false);
        hideShiftWarning(true);
    });

    // Ensure edit modal displays correctly when opened
    $('#editExplanationModal').on('shown.bs.modal', function() {
        // Focus on modal to ensure it's on top
        $(this).focus();

        // Ensure correct z-index
        var zIndex = 1060;
        $(this).css('z-index', zIndex);
        $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');

        // Re-initialize Select2 for edit modal to ensure proper functionality
        $('#edit_tagged_user_id').select2('destroy').select2({
            placeholder: 'Chọn 1 đồng nghiệp để xác nhận...',
            allowClear: true,
            width: '100%',
            dropdownParent: $('#editExplanationModal')
        });
    });

    // Click on day to view details
    $('.calendar-day').click(function() {
        var date = $(this).data('date');
        if (date && $(this).find('.attendance-info').length > 0) {
            // Can add modal to show attendance details
            console.log('Show detail for date:', date);
        }
    });

    // Event handlers for action buttons
    $(document).on('click', '.btn-edit-explanation', function() {
        var explanationId = $(this).data('id');
        editExplanation(explanationId);
    });

    $(document).on('click', '.btn-delete-explanation', function() {
        var explanationId = $(this).data('id');

        if (confirm('Bạn có chắc chắn muốn xóa giải trình này?')) {
            $.ajax({
                url: '/attendance-explanation/' + explanationId,
                type: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        showToast('success', 'Thành công', response.message);
                        // Reload page to update statistics
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        showToast('error', 'Lỗi', response.message);
                    }
                },
                error: function(xhr) {
                    var message = xhr.responseJSON ? xhr.responseJSON.message : 'Có lỗi xảy ra';
                    showToast('error', 'Lỗi', message);
                }
            });
        }
    });

    $(document).on('click', '.btn-pause-explanation', function() {
        var explanationId = $(this).data('id');

        if (confirm('Bạn có chắc chắn muốn tạm dừng giải trình này?')) {
            $.ajax({
                url: '/attendance-explanation/' + explanationId + '/pause',
                type: 'PATCH',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        showToast('success', 'Thành công', response.message);
                        // Reload modal to update status
                        var currentDateText = $('#view_selected_date').text();
                        console.log('Pause success - current date text:', currentDateText);
                        // Parse date from text "DD/MM/YYYY" to format YYYY-MM-DD
                        var dateMatch = currentDateText.match(/(\d{2})\/(\d{2})\/(\d{4})/);
                        if (dateMatch) {
                            var formattedDate = dateMatch[3] + '-' + dateMatch[2] + '-' + dateMatch[1]; // YYYY-MM-DD
                            console.log('Parsed date for reload:', formattedDate);
                            loadExplanations(formattedDate);
                            // Reload page after 2 seconds to update monthly statistics
                            setTimeout(function() {
                                location.reload();
                            }, 2000);
                        } else {
                            console.error('Could not parse date from text:', currentDateText);
                            // Fallback: reload page if date cannot be parsed
                            location.reload();
                        }
                    } else {
                        showToast('error', 'Lỗi', response.message);
                    }
                },
                error: function(xhr) {
                    var message = xhr.responseJSON ? xhr.responseJSON.message : 'Có lỗi xảy ra';
                    showToast('error', 'Lỗi', message);
                }
            });
        }
    });

    $(document).on('click', '.btn-resume-explanation', function() {
        var explanationId = $(this).data('id');

        if (confirm('Bạn có chắc chắn muốn tiếp tục giải trình này?')) {
            $.ajax({
                url: '/attendance-explanation/' + explanationId + '/resume',
                type: 'PATCH',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        showToast('success', 'Thành công', response.message);
                        // Reload modal to update status
                        var currentDateText = $('#view_selected_date').text();
                        console.log('Resume success - current date text:', currentDateText);
                        // Parse date from text "DD/MM/YYYY" to format YYYY-MM-DD
                        var dateMatch = currentDateText.match(/(\d{2})\/(\d{2})\/(\d{4})/);
                        if (dateMatch) {
                            var formattedDate = dateMatch[3] + '-' + dateMatch[2] + '-' + dateMatch[1]; // YYYY-MM-DD
                            console.log('Parsed date for reload:', formattedDate);
                            loadExplanations(formattedDate);
                            // Reload page after 2 seconds to update monthly statistics
                            setTimeout(function() {
                                location.reload();
                            }, 2000);
                        } else {
                            console.error('Could not parse date from text:', currentDateText);
                            // Fallback: reload page if date cannot be parsed
                            location.reload();
                        }
                    } else {
                        showToast('error', 'Lỗi', response.message);
                    }
                },
                error: function(xhr) {
                    var message = xhr.responseJSON ? xhr.responseJSON.message : 'Có lỗi xảy ra';
                    showToast('error', 'Lỗi', message);
                }
            });
        }
    });
});

/**
 * Function to load explanations list
 */
function loadExplanations(date) {
    console.log('Loading explanations for date:', date);
    $('#explanations_list').html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Đang tải...</div>');

    $.ajax({
        url: window.routes.attendanceExplanationApi.replace(':date', date),
        method: 'GET',
        timeout: 10000, // 10 seconds timeout
        success: function(response) {
            console.log('API response:', response);
            if (response.success && response.data && response.data.explanations) {
                var html = '';
                var explanations = response.data.explanations;

                if (explanations.length === 0) {
                    html = '<div class="alert alert-info">Không có giải trình nào cho ngày này.</div>';
                } else {
                    explanations.forEach(function(explanation, index) {
                        html += buildExplanationHtml(explanation);
                    });
                }

                $('#explanations_list').html(html);
            } else {
                console.error('Invalid API response:', response);
                $('#explanations_list').html('<div class="alert alert-danger">Dữ liệu trả về không hợp lệ. Vui lòng thử lại.</div>');
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX error:', {xhr: xhr, status: status, error: error, date: date});
            var errorMessage = 'Có lỗi xảy ra khi tải dữ liệu.';

            if (xhr.status === 404) {
                errorMessage = 'Không tìm thấy dữ liệu cho ngày này.';
            } else if (xhr.status === 500) {
                errorMessage = 'Lỗi server. Vui lòng thử lại sau.';
            } else if (status === 'timeout') {
                errorMessage = 'Timeout. Vui lòng kiểm tra kết nối mạng.';
            }

            $('#explanations_list').html('<div class="alert alert-danger">' + errorMessage + '</div>');
        }
    });
}

/**
 * Load shifts with rules for specific date
 */
function loadShiftsWithRules(date) {
    console.log('Loading shifts with rules for date:', date);

    $.ajax({
        url: '/api/v1/attendance-explanations/shifts-with-rules',
        method: 'GET',
        data: { date: date },
        success: function(response) {
            console.log('Shifts with rules response:', response);

            if (response.success && response.data) {
                // Update create modal
                var createSelect = $('#shift_change_id');
                createSelect.empty();
                createSelect.append('<option value="">-- Chọn ca làm việc mới --</option>');

                response.data.forEach(function(shift) {
                    var optionText = shift.display_with_rule_info;
                    createSelect.append(
                        '<option value="' + shift.id + '" ' +
                        'data-start-time="' + shift.start_time + '" ' +
                        'data-end-time="' + shift.end_time + '" ' +
                        'data-has-rule="' + shift.has_rule + '">' +
                        optionText + '</option>'
                    );
                });

                // Update edit modal
                var editSelect = $('#edit_shift_change_id');
                editSelect.empty();
                editSelect.append('<option value="">-- Chọn ca làm việc mới --</option>');

                response.data.forEach(function(shift) {
                    var optionText = shift.display_with_rule_info;
                    editSelect.append(
                        '<option value="' + shift.id + '" ' +
                        'data-start-time="' + shift.start_time + '" ' +
                        'data-end-time="' + shift.end_time + '" ' +
                        'data-has-rule="' + shift.has_rule + '">' +
                        optionText + '</option>'
                    );
                });

                console.log('Successfully loaded ' + response.data.length + ' shifts with rules');
            } else {
                console.error('Invalid response for shifts with rules:', response);
                showToast('error', 'Lỗi', 'Không thể tải danh sách ca làm việc');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading shifts with rules:', {xhr: xhr, status: status, error: error});
            showToast('error', 'Lỗi', 'Có lỗi xảy ra khi tải danh sách ca làm việc');
        }
    });
}

/**
 * Function to build explanation HTML
 */
function buildExplanationHtml(explanation) {
    // Determine final status
    var finalStatusClass = explanation.final_status === 'approved' ? 'success' :
                         explanation.final_status === 'rejected' ? 'danger' :
                         explanation.final_status === 'paused' ? 'info' : 'warning';
    var finalStatusText = explanation.final_status === 'approved' ? 'Hoàn tất' :
                        explanation.final_status === 'rejected' ? 'Bị từ chối' :
                        explanation.final_status === 'paused' ? 'Đã tạm dừng' : 'Đang xử lý';

    var html = '<div class="panel panel-default">';
    html += '<div class="panel-heading">';
    html += '<h5 class="panel-title">';
    html += '<span class="label label-' + finalStatusClass + '">' + finalStatusText + '</span> ';
    html += getExplanationTypeText(explanation.explanation_type);
    html += '<small class="pull-right text-muted">' + moment(explanation.created_at).format('DD/MM/YYYY HH:mm') + '</small>';
    html += '</h5>';
    html += '</div>';
    html += '<div class="panel-body">';
    html += '<p>' + explanation.explanation + '</p>';

    // Show OT hours if available
    if (explanation.explanation_type === 'overtime' && explanation.ot_hours) {
        html += '<p><strong>Số giờ OT:</strong> <span class="label label-info">' + explanation.ot_hours + ' giờ</span></p>';
    }

    // Show remote work shift if available
    if (explanation.explanation_type === 'remote_work' && explanation.remote_shift) {
        html += '<p><strong>Ca làm việc remote:</strong> <span class="label label-primary">' + explanation.remote_shift.name + '</span></p>';
    }

    // Show adjustment info if approved
    if (explanation.final_status === 'approved') {
        html += '<div class="alert alert-success" style="margin-top: 10px; padding: 8px;">';
        html += '<i class="fa fa-check-circle"></i> <strong>Đã áp dụng điều chỉnh:</strong><br>';

        if (explanation.explanation_type === 'overtime') {
            html += '• Thêm ' + explanation.ot_hours + ' giờ OT';
        } else {
            html += '• Điều chỉnh thành 1.0 công (từ dữ liệu chấm công gốc)';
        }
        html += '</div>';
    }

    // Show 2-step workflow
    html += '<div class="row">';
    html += '<div class="col-md-6">';
    html += '<h6><i class="fa fa-user"></i> Bước 1: Manager</h6>';
    var managerStatusClass = explanation.manager_status === 'approved' ? 'success' :
                           explanation.manager_status === 'rejected' ? 'danger' : 'warning';
    var managerStatusText = explanation.manager_status === 'approved' ? 'Đã duyệt' :
                          explanation.manager_status === 'rejected' ? 'Từ chối' : 'Chờ duyệt';
    html += '<span class="label label-' + managerStatusClass + '">' + managerStatusText + '</span>';
    if (explanation.manager_approved_at) {
        html += '<br><small class="text-muted">' + moment(explanation.manager_approved_at).format('DD/MM/YYYY HH:mm') + '</small>';
    }
    if (explanation.manager_note) {
        html += '<br><small class="text-info">' + explanation.manager_note + '</small>';
    }
    html += '</div>';

    html += '<div class="col-md-6">';
    html += '<h6><i class="fa fa-building"></i> Bước 2: HCNS</h6>';
    var hrStatusClass = explanation.hr_status === 'approved' ? 'success' :
                      explanation.hr_status === 'rejected' ? 'danger' : 'warning';
    var hrStatusText = explanation.hr_status === 'approved' ? 'Đã duyệt' :
                     explanation.hr_status === 'rejected' ? 'Từ chối' : 'Chờ duyệt';
    html += '<span class="label label-' + hrStatusClass + '">' + hrStatusText + '</span>';
    if (explanation.hr_approved_at) {
        html += '<br><small class="text-muted">' + moment(explanation.hr_approved_at).format('DD/MM/YYYY HH:mm') + '</small>';
    }
    if (explanation.hr_note) {
        html += '<br><small class="text-info">' + explanation.hr_note + '</small>';
    }
    html += '</div>';
    html += '</div>';

    // Show tagged user info if available
    if (explanation.tagged_user_id) {
        html += '<hr>';
        html += '<div class="row">';
        html += '<div class="col-md-12">';
        html += '<h6><i class="fa fa-user-check"></i> Bước 0: Xác nhận từ đồng nghiệp</h6>';

        var taggedStatusClass = explanation.tagged_user_status === 'confirmed' ? 'success' :
                              explanation.tagged_user_status === 'rejected' ? 'danger' : 'warning';
        var taggedStatusText = explanation.tagged_user_status === 'confirmed' ? 'Đã xác nhận' :
                             explanation.tagged_user_status === 'rejected' ? 'Đã từ chối' : 'Chờ xác nhận';

        html += '<p><strong>Người được tag:</strong> ';
        if (explanation.tagged_user_name) {
            html += '<span class="text-primary">' + explanation.tagged_user_name + '</span>';
        } else {
            html += '<span class="text-muted">N/A</span>';
        }
        html += '</p>';

        html += '<p><strong>Trạng thái:</strong> ';
        html += '<span class="label label-' + taggedStatusClass + '">' + taggedStatusText + '</span>';
        html += '</p>';

        if (explanation.tagged_user_confirmed_at) {
            html += '<p><strong>Thời gian xác nhận:</strong> ';
            html += '<small class="text-muted">' + moment(explanation.tagged_user_confirmed_at).format('DD/MM/YYYY HH:mm') + '</small>';
            html += '</p>';
        }

        if (explanation.tagged_user_note) {
            html += '<p><strong>Ghi chú:</strong> ';
            html += '<small class="text-info">' + explanation.tagged_user_note + '</small>';
            html += '</p>';
        }

        html += '</div>';
        html += '</div>';
    }

    // Warning when manager approved
    if (explanation.manager_status === 'approved') {
        html += '<div class="alert alert-warning" style="margin-top: 10px; padding: 8px;">';
        html += '<i class="fa fa-lock"></i> <strong>Đã được manager phê duyệt:</strong> Không thể xóa hoặc tạm dừng giải trình này.';
        html += '</div>';
    }

    // Add action buttons
    html += '<hr>';
    html += '<div class="explanation-actions">';

    // Edit button (only show if can update)
    if (explanation.can_update) {
        html += '<button class="btn btn-sm btn-primary btn-edit-explanation" data-id="' + explanation.id + '" title="Sửa giải trình">';
        html += '<i class="fa fa-edit"></i> Sửa';
        html += '</button> ';
    }

    // Delete button (only show if not fully approved and manager not approved)
    if (explanation.final_status !== 'approved' && explanation.manager_status !== 'approved') {
        html += '<button class="btn btn-sm btn-danger btn-delete-explanation" data-id="' + explanation.id + '" title="Xóa giải trình">';
        html += '<i class="fa fa-trash"></i> Xóa';
        html += '</button> ';
    }

    // Pause/Resume button (only show if manager not approved)
    if (explanation.final_status === 'pending' && explanation.manager_status !== 'approved') {
        html += '<button class="btn btn-sm btn-info btn-pause-explanation" data-id="' + explanation.id + '" title="Tạm dừng giải trình">';
        html += '<i class="fa fa-pause"></i> Tạm dừng';
        html += '</button>';
    } else if (explanation.final_status === 'paused' && explanation.manager_status !== 'approved') {
        html += '<button class="btn btn-sm btn-success btn-resume-explanation" data-id="' + explanation.id + '" title="Tiếp tục giải trình">';
        html += '<i class="fa fa-play"></i> Tiếp tục';
        html += '</button>';
    }

    html += '</div>';
    html += '</div>';
    html += '</div>';

    return html;
}

/**
 * Function to get explanation type text
 */
function getExplanationTypeText(type) {
    var types = {
        'late': 'Đi muộn',
        'early': 'Về sớm',
        'insufficient_hours': 'Thiếu giờ làm việc',
        'no_checkin': 'Quên checkin',
        'no_checkout': 'Quên checkout',
        'overtime': 'Làm thêm giờ (OT)',
        'remote_work': 'Làm việc remote',
        'shift_change': 'Thay đổi ca làm việc',
        'new_employee_no_account': 'Người mới đi làm chưa được cấp tài khoản',
        'other': 'Khác'
    };
    return types[type] || 'Không xác định';
}

/**
 * Function to edit explanation
 */
window.editExplanation = function(explanationId) {
    // Close detail modal first (if open)
    $('#viewExplanationsModal').modal('hide');

    // Wait for detail modal to close completely before opening edit modal
    setTimeout(function() {
        $.get('/attendance-explanation/' + explanationId + '/edit')
            .done(function(response) {
                if (response.success) {
                    var data = response.data;

                    // Fill data into form
                    $('#edit_explanation_id').val(data.id);
                    $('#edit_selected_date').val(moment(data.date).format('DD/MM/YYYY'));
                    $('#edit_explanation_type').val(data.explanation_type).trigger('change');
                    $('#edit_explanation').val(data.explanation);
                    $('#edit_ot_hours').val(data.ot_hours);
                    $('#edit_remote_shift_id').val(data.remote_shift_id);
                    $('#edit_shift_change_id').val(data.shift_change_id);

                    // Store date for shift validation in edit mode
                    window.currentExplanationDate = data.date;

                    // Load shifts with rules for this date
                    loadShiftsWithRules(data.date);

                    if (data.explanation_type === 'shift_change') {
                        loadTimesheetForValidation(data.date);
                    }

                    // Set value for Select2 tagged user
                    if (data.tagged_user_id) {
                        $('#edit_tagged_user_id').val(data.tagged_user_id);
                    } else {
                        $('#edit_tagged_user_id').val('');
                    }

                    // Set action URL for form
                    $('#editExplanationForm').attr('action', '/attendance-explanation/' + data.id);

                    // Show edit modal with higher z-index
                    $('#editExplanationModal').modal({
                        backdrop: 'static',
                        keyboard: false
                    }).modal('show');

                    // Trigger change for Select2 after modal is shown
                    setTimeout(function() {
                        $('#edit_tagged_user_id').trigger('change');
                    }, 100);
                } else {
                    showToast('error', 'Lỗi', response.message || 'Không thể tải thông tin giải trình');
                }
            })
            .fail(function(xhr) {
                var message = 'Có lỗi xảy ra khi tải thông tin giải trình';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                showToast('error', 'Lỗi', message);
            });
    }, 300); // Wait 300ms for detail modal to close completely
};

/**
 * Handle edit form submit
 */
$('#editExplanationForm').submit(function(e) {
    e.preventDefault();

    var formData = $(this).serialize();
    var explanationId = $('#edit_explanation_id').val();

    $.ajax({
        url: '/attendance-explanation/' + explanationId,
        method: 'PUT',
        data: formData,
        success: function(response) {
            if (response.success) {
                showToast('success', 'Thành công', response.message);
                $('#editExplanationModal').modal('hide');

                // Reload page to update data
                setTimeout(function() {
                    location.reload();
                }, 1000);
            } else {
                showToast('error', 'Lỗi', response.message || 'Có lỗi xảy ra');
            }
        },
        error: function(xhr) {
            var message = 'Có lỗi xảy ra khi cập nhật giải trình';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                var errors = xhr.responseJSON.errors;
                message = Object.values(errors).flat().join('<br>');
            }
            showToast('error', 'Lỗi', message);
        }
    });
});

/**
 * Function to show toast notification
 */
function showToast(type, title, message) {
    var toastClass = type === 'success' ? 'alert-success' : 'alert-danger';
    var iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';

    var toastHtml = '<div class="alert ' + toastClass + ' alert-dismissible toast-notification" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">' +
        '<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>' +
        '<h5><i class="fa ' + iconClass + '"></i> ' + title + '</h5>' +
        message +
    '</div>';

    $('body').append(toastHtml);

    // Auto hide after 5 seconds
    setTimeout(function() {
        $('.toast-notification').fadeOut(500, function() {
            $(this).remove();
        });
    }, 5000);
}

// Global variable to store timesheet data for validation
window.currentTimesheetData = null;

/**
 * Load timesheet data for shift validation
 */
function loadTimesheetForValidation(date) {
    $.ajax({
        url: '/attendance-explanation/timesheet-validation/' + date,
        method: 'GET',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
            'Accept': 'application/json'
        },
        success: function(response) {
            if (response.success && response.data.has_timesheet) {
                window.currentTimesheetData = response.data;
                console.log('Loaded timesheet data:', response.data);
            } else {
                window.currentTimesheetData = null;
                console.log('No timesheet data for date:', date);
            }
        },
        error: function(xhr) {
            console.error('Error loading timesheet data:', xhr);
            window.currentTimesheetData = null;
        }
    });
}

/**
 * Validate shift selection against timesheet data
 */
function validateShiftSelection(selectedShiftId, isEdit) {
    var prefix = isEdit ? 'edit_' : '';
    var $shiftSelect = $('#' + prefix + 'shift_change_id');
    var $selectedOption = $shiftSelect.find('option[value="' + selectedShiftId + '"]');

    if (!$selectedOption.length || !window.currentTimesheetData) {
        hideShiftWarning(isEdit);
        return;
    }

    var shiftStartTime = $selectedOption.data('start-time');
    var shiftEndTime = $selectedOption.data('end-time');
    var checkinTime = window.currentTimesheetData.earliest_checkin;
    var checkoutTime = window.currentTimesheetData.latest_checkout;

    console.log('Validating shift selection:', {
        selectedShiftId: selectedShiftId,
        shiftStartTime: shiftStartTime,
        shiftEndTime: shiftEndTime,
        checkinTime: checkinTime,
        checkoutTime: checkoutTime,
        timesheetData: window.currentTimesheetData
    });

    if (!checkinTime || !checkoutTime || !shiftStartTime || !shiftEndTime) {
        console.log('Missing required data for validation');
        hideShiftWarning(isEdit);
        return;
    }

    // Convert times to minutes for comparison
    var shiftStart = timeToMinutes(shiftStartTime);
    var shiftEnd = timeToMinutes(shiftEndTime);
    var checkin = timeToMinutes(checkinTime);
    var checkout = timeToMinutes(checkoutTime);

    // Calculate actual work duration and shift duration (in hours)
    var actualWorkMinutes = checkout - checkin;
    var shiftDurationMinutes = shiftEnd - shiftStart;
    var actualWorkHours = (actualWorkMinutes / 60).toFixed(1);
    var shiftDurationHours = (shiftDurationMinutes / 60).toFixed(1);

    var warnings = [];

    // Check if actual work time is significantly longer than shift duration (more than 1 hour difference)
    if (actualWorkMinutes > shiftDurationMinutes + 60) {
        warnings.push('Bạn đã làm việc ' + actualWorkHours + ' tiếng (' + checkinTime + ' - ' + checkoutTime + ') nhưng ca được chọn chỉ có ' + shiftDurationHours + ' tiếng (' + shiftStartTime + ' - ' + shiftEndTime + '). Nên chọn ca làm việc dài hơn.');
    }

    // Check if actual work time is significantly shorter than shift duration (more than 1 hour difference)
    if (actualWorkMinutes < shiftDurationMinutes - 60) {
        warnings.push('Bạn chỉ làm việc ' + actualWorkHours + ' tiếng (' + checkinTime + ' - ' + checkoutTime + ') nhưng ca được chọn có ' + shiftDurationHours + ' tiếng (' + shiftStartTime + ' - ' + shiftEndTime + '). Nên chọn ca làm việc ngắn hơn.');
    }

    console.log('Validation result:', {
        actualWorkHours: actualWorkHours,
        shiftDurationHours: shiftDurationHours,
        warnings: warnings
    });

    if (warnings.length > 0) {
        console.log('Showing warning:', warnings.join(' '));
        showShiftWarning(warnings.join(' '), isEdit);
    } else {
        console.log('No warnings to show');
        hideShiftWarning(isEdit);
    }
}

/**
 * Convert time string (HH:MM) to minutes
 */
function timeToMinutes(timeStr) {
    var parts = timeStr.split(':');
    return parseInt(parts[0]) * 60 + parseInt(parts[1]);
}

/**
 * Show shift warning message
 */
function showShiftWarning(message, isEdit) {
    var prefix = isEdit ? 'edit_' : '';
    $('#' + prefix + 'shift_warning_message').text(message);
    $('#' + prefix + 'shift_warning').show();
}

/**
 * Hide shift warning message
 */
function hideShiftWarning(isEdit) {
    var prefix = isEdit ? 'edit_' : '';
    $('#' + prefix + 'shift_warning').hide();
}
