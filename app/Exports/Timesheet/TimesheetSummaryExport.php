<?php

namespace App\Exports\Timesheet;

use App\Models\User;
use App\Models\Timesheet;
use App\Models\AttendanceExplanation;
use App\Models\Holiday;
use App\Repositories\Eloquent\TimesheetRepository;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;

class TimesheetSummaryExport implements FromCollection, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    private $fromDate;
    private $toDate;
    private $conditions;
    private $holidayCount;
    // Removed violation rows tracking for summary export

    public function __construct($conditions = [])
    {
        $this->conditions = $conditions;
        $this->fromDate = Carbon::parse($conditions['from_date'] ?? now()->startOfMonth());
        $this->toDate = Carbon::parse($conditions['to_date'] ?? now()->endOfMonth());
        
        // Tính số ngày lễ trong khoảng thời gian
        $this->holidayCount = $this->calculateHolidayCount();
    }

    public function collection()
    {
        // Lấy danh sách users
        $users = User::query()
            ->whereNull('deleted_at') // Loại bỏ soft deleted users
            ->where('active_flag', true) // Loại bỏ user deactive
            ->where('is_exportable', true) // Chỉ lấy users có thể export
            ->when(isset($this->conditions['user_ids']), function ($query) {
                return $query->whereIn('id', $this->conditions['user_ids']);
            })
            ->when(isset($this->conditions['department_ids']), function ($query) {
                return $query->whereIn('staff_department_id', $this->conditions['department_ids']);
            })
            ->with(['staffDepartment'])
            ->orderBy('staff_department_id')
            ->orderBy('staff_department_chief_flag', 'desc')
            ->orderBy('name')
            ->get();

        $data = collect();
        foreach ($users as $index => $user) {
            $summaryData = $this->calculateUserSummary($user);
            $data->push($summaryData);
        }

        return $data;
    }

    private function calculateUserSummary($user)
    {
        // Lấy tất cả timesheet trong khoảng thời gian với shift_rule_histories data
        $timesheetRepository = app(\App\Contracts\Repositories\TimesheetRepositoryInterface::class);
        $timesheets = $timesheetRepository->queryToExport([
            'user_ids' => [$user->id],
            'from_date' => $this->fromDate->format('Y-m-d'),
            'to_date' => $this->toDate->format('Y-m-d'),
        ])->get();

        // Lấy giải trình đã được duyệt
        $approvedExplanations = AttendanceExplanation::where('user_id', $user->id)
            ->whereBetween('date', [$this->fromDate, $this->toDate])
            ->where('final_status', 'approved')
            ->get();

        // Lấy danh sách ngày lễ
        $holidays = Holiday::active()
            ->whereBetween('date', [$this->fromDate, $this->toDate])
            ->pluck('date')
            ->map(fn($d) => Carbon::parse($d)->toDateString())
            ->toArray();

        // Tính toán các chỉ số
        $probationWorkdays = 0;
        $officialWorkdays = 0;
        $holidayWorkdays = $this->calculateUserHolidayWorkdays($user); // Sử dụng method riêng cho user
        $onlineWorkdays = 0;
        $sundayWorkdays = 0;
        $totalPenalty = 0;
        $holidayAttendance = 0; // Công đi làm ngày lễ
        $absentDays = 0; // Số ngày nghỉ không phép

        // Tính tổng giờ OT từ TẤT CẢ giải trình OT được duyệt (bao gồm cả những ngày không có timesheet)
        $overtimeHours = $approvedExplanations->where('explanation_type', 'overtime')->sum('ot_hours') ?? 0;

        foreach ($timesheets as $timesheet) {
            $date = Carbon::parse($timesheet->date);
            $dateString = $date->toDateString();

            // Kiểm tra có checkin/checkout hợp lệ vào ngày lễ không
            if (in_array($dateString, $holidays) && $timesheet->checkin && $timesheet->checkout) {
                $holidayAttendance += 1;
            }

            // Kiểm tra có giải trình được duyệt không
            $explanation = $approvedExplanations->where('date', $timesheet->date)->first();

            if ($explanation) {
                // Có giải trình được duyệt
                if ($explanation->explanation_type === 'overtime') {
                    // Giờ OT đã được tính ở trên rồi, chỉ cần tính công
                    // Ngày có OT vẫn tính công bình thường
                    $workday = $this->calculateWorkday($timesheet);
                    if ($this->isInProbationPeriod($user, $date)) {
                        $probationWorkdays += $workday;
                    } else {
                        $officialWorkdays += $workday;
                    }
                } elseif ($explanation->explanation_type === 'remote_work') {
                    $onlineWorkdays += 1;
                } else {
                    // Các loại giải trình khác (bao gồm new_employee_no_account) được tính 1 công
                    if ($this->isInProbationPeriod($user, $date)) {
                        $probationWorkdays += 1;
                    } else {
                        $officialWorkdays += 1;
                    }
                }
            } else {
                // Không có giải trình, tính công từ máy
                $workday = $this->calculateWorkday($timesheet);

                if ($this->isInProbationPeriod($user, $date)) {
                    $probationWorkdays += $workday;
                } else {
                    $officialWorkdays += $workday;
                }

                // Tính phạt nếu có vi phạm
                $penalty = $this->calculatePenalty($timesheet, $user);
                $totalPenalty += $penalty;
            }


            
            // Tính làm việc chủ nhật (chỉ tính nếu có checkin/checkout)
            if ($date->dayOfWeek === Carbon::SUNDAY && $timesheet->checkin && $timesheet->checkout) {
                $sundayWorkdays += 1;
            }
        }

        $actualWorkdays = $probationWorkdays + $officialWorkdays;
        $totalWorkdays = $actualWorkdays + $holidayWorkdays + $onlineWorkdays + $sundayWorkdays;

        return [
            'code' => $user->code ?: '',
            'name' => $user->name,
            'department' => $user->staffDepartment->name ?? '',
            'start_date' => $user->start_date ? Carbon::parse($user->start_date)->format('d/m/Y') : '',
            'probation_end_date' => $user->probation_end_date ? Carbon::parse($user->probation_end_date)->format('d/m/Y') : '',
            'end_date' => $user->end_date ? Carbon::parse($user->end_date)->format('d/m/Y') : '',
            'probation_workdays' => number_format($probationWorkdays, 2),
            'official_workdays' => number_format($officialWorkdays, 2),
            'holiday_workdays' => number_format($holidayWorkdays, 2),
            'holiday_attendance' => $holidayAttendance, // Công đi làm ngày lễ
            'online_workdays' => number_format($onlineWorkdays, 2),
            'actual_workdays' => number_format($actualWorkdays, 2),
            'total_workdays' => number_format($totalWorkdays, 2),
            'sunday_workdays' => $sundayWorkdays,
            'total_penalty' => number_format($totalPenalty, 0, ',', '.'),
            'overtime_hours' => number_format($overtimeHours, 1),
        ];
    }



    /**
     * Tính số ngày lễ trong khoảng thời gian export
     */
    private function calculateHolidayCount()
    {
        return Holiday::active()
            ->whereBetween('date', [$this->fromDate, $this->toDate])
            ->count();
    }

    private function calculateHolidayWorkdays()
    {
        // Sử dụng model Holiday thay vì hardcode
        $holidays = Holiday::active()
            ->whereBetween('date', [$this->fromDate, $this->toDate])
            ->get();

        return $holidays->count();
    }

    /**
     * Tính số ngày lễ hưởng lương riêng cho từng user dựa trên thời gian làm việc
     * Chỉ tính ngày lễ nếu user đã bắt đầu làm việc TRƯỚC ngày lễ đó
     */
    private function calculateUserHolidayWorkdays($user)
    {
        // Nếu user chưa có ngày bắt đầu làm việc, không tính ngày lễ nào
        if (!$user->start_date) {
            return 0;
        }

        $userStartDate = Carbon::parse($user->start_date);
        $userEndDate = $user->end_date ? Carbon::parse($user->end_date) : $this->toDate;

        // Giới hạn trong khoảng export
        $effectiveStartDate = max($userStartDate, $this->fromDate);
        $effectiveEndDate = min($userEndDate, $this->toDate);

        // Lấy tất cả ngày lễ trong khoảng export
        $holidays = Holiday::active()
            ->whereBetween('date', [$this->fromDate, $this->toDate])
            ->get();

        $holidayCount = 0;

        foreach ($holidays as $holiday) {
            $holidayDate = Carbon::parse($holiday->date);

            // Chỉ tính ngày lễ nếu:
            // 1. User đã bắt đầu làm việc TRƯỚC ngày lễ (không phải cùng ngày)
            // 2. Ngày lễ nằm trong khoảng thời gian làm việc của user
            // 3. User chưa nghỉ việc hoặc nghỉ việc sau ngày lễ
            if ($userStartDate->lt($holidayDate) &&
                $holidayDate->between($effectiveStartDate, $effectiveEndDate)) {
                $holidayCount++;
            }
        }

        return $holidayCount;
    }

    private function isInProbationPeriod($user, $date)
    {
        if (!$user->start_date || !$user->probation_end_date) {
            return false;
        }
        
        $startDate = Carbon::parse($user->start_date);
        $probationEndDate = Carbon::parse($user->probation_end_date);
        
        return $date->between($startDate, $probationEndDate);
    }

    private function calculateWorkday($timesheet)
    {
        if (!$timesheet->checkin || !$timesheet->checkout) {
            return 0;
        }

        // Kiểm tra logic 120 phút cho vi phạm
        $checkin = \Carbon\Carbon::parse($timesheet->checkin);
        $checkout = \Carbon\Carbon::parse($timesheet->checkout);
        $dateString = $checkin->format('Y-m-d');
        $expectedCheckin = \Carbon\Carbon::parse($dateString . ' ' . $timesheet->shift_start_time);
        $expectedCheckout = \Carbon\Carbon::parse($dateString . ' ' . $timesheet->shift_end_time);

        $lateMinutes = $checkin->gt($expectedCheckin) ? $checkin->diffInMinutes($expectedCheckin) : 0;
        $earlyMinutes = $checkout->lt($expectedCheckout) ? $expectedCheckout->diffInMinutes($checkout) : 0;

        // Logic mới: Nếu vi phạm >= 120 phút thì tính công bình thường (có thể bị trừ)
        $hasLargeLateViolation = $lateMinutes >= 120;
        $hasLargeEarlyViolation = $earlyMinutes >= 120;

        if ($hasLargeLateViolation || $hasLargeEarlyViolation) {
            // Vi phạm >= 120 phút: tính công bình thường (có thể bị trừ)
            $workdayHours = $this->calculateWorkdayHours($timesheet);
            $workdayMin1 = $timesheet->shift_workday_min_1 ?? 7.5;
            $workdayMin2 = $timesheet->shift_workday_min_2 ?? 4.5;

            if ($workdayHours >= $workdayMin1) {
                return $timesheet->shift_default_workday_threshold ?? 1.0; // Sử dụng threshold từ shift
            } elseif ($workdayHours >= $workdayMin2) {
                return 0.5;
            } else {
                return 0;
            }
        }

        // Vi phạm < 120 phút hoặc không vi phạm: force đủ công
        return $timesheet->shift_default_workday_threshold ?? 1.0; // Sử dụng threshold từ shift
    }

    private function calculateWorkdayHours($timesheet)
    {
        $checkin = \Carbon\Carbon::parse($timesheet->checkin);
        $checkout = \Carbon\Carbon::parse($timesheet->checkout);

        // Apply business rule: if checkin before shift start, use shift start
        $dateString = $checkin->format('Y-m-d');
        $shiftStart = \Carbon\Carbon::parse($dateString . ' ' . $timesheet->shift_start_time);
        $shiftEnd = \Carbon\Carbon::parse($dateString . ' ' . $timesheet->shift_end_time);

        $effectiveStart = $checkin->lt($shiftStart) ? $shiftStart : $checkin;
        $effectiveEnd = $checkout->gt($shiftEnd) ? $shiftEnd : $checkout;

        // Tính tổng giờ làm việc với effective times
        $totalHours = $effectiveEnd->floatDiffInHours($effectiveStart);

        // Lấy break times từ shift rule thay vì hardcode
        $breakHours = $this->calculateBreakHours($timesheet, $effectiveStart, $effectiveEnd);

        $workdayHours = $totalHours - $breakHours;

        return $workdayHours > 0 ? $workdayHours : 0;
    }

    /**
     * Calculate break hours based on shift configuration
     */
    private function calculateBreakHours($timesheet, $checkin, $checkout)
    {
        // Get break times from shift rule (handle double-encoded JSON)
        $breakTimes = null;
        if ($timesheet->shift_break_times) {
            $rawBreakTimes = $timesheet->shift_break_times;

            if (is_string($rawBreakTimes)) {
                // First decode
                $firstDecode = json_decode($rawBreakTimes, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    // Check if result is still a string (double-encoded)
                    if (is_string($firstDecode)) {
                        // Second decode
                        $breakTimes = json_decode($firstDecode, true);
                        if (json_last_error() !== JSON_ERROR_NONE) {
                            return 0; // Invalid JSON, no break time
                        }
                    } else {
                        $breakTimes = $firstDecode;
                    }
                } else {
                    return 0; // Invalid JSON, no break time
                }
            } else {
                $breakTimes = $rawBreakTimes;
            }
        }

        if (empty($breakTimes) || !is_array($breakTimes)) {
            return 0; // No break time if not configured or invalid
        }

        $totalBreakHours = 0;
        $workStart = $checkin;
        $workEnd = $checkout;

        foreach ($breakTimes as $breakTime) {
            if (!isset($breakTime['start']) || !isset($breakTime['end'])) {
                continue;
            }

            $breakStart = \Carbon\Carbon::parse($workStart->format('Y-m-d') . ' ' . $breakTime['start']);
            $breakEnd = \Carbon\Carbon::parse($workStart->format('Y-m-d') . ' ' . $breakTime['end']);

            // Only count break time that overlaps with actual work time
            $overlapStart = $workStart->gt($breakStart) ? $workStart : $breakStart;
            $overlapEnd = $workEnd->lt($breakEnd) ? $workEnd : $breakEnd;

            if ($overlapStart->lt($overlapEnd)) {
                $totalBreakHours += $overlapEnd->floatDiffInHours($overlapStart);
            }
        }

        return round($totalBreakHours, 2);
    }

    private function calculatePenalty($timesheet, $user)
    {
        // Kiểm tra có đủ thông tin để tính phạt không
        if (!$timesheet->checkin || !$timesheet->checkout || !$timesheet->shift_start_time || !$timesheet->shift_end_time) {
            return 0;
        }

        // Tính workday để kiểm tra
        $workday = $this->calculateWorkday($timesheet);

        // Luôn tính penalty bất kể workday (vì đi muộn/về sớm vẫn là vi phạm)

        $checkin = Carbon::parse($timesheet->checkin);
        $checkout = Carbon::parse($timesheet->checkout);
        $timesheetDate = Carbon::parse($timesheet->date);

        // Lấy thời gian ca làm việc từ query (đã ưu tiên shift_rule_histories)
        // Fix: Sử dụng format date string thay vì Carbon object
        $dateString = $timesheetDate->format('Y-m-d');
        $expectedCheckin = \Carbon\Carbon::parse($dateString . ' ' . $timesheet->shift_start_time);
        $expectedCheckout = \Carbon\Carbon::parse($dateString . ' ' . $timesheet->shift_end_time);

        $totalPenalty = 0;
        $isChief = $user->staff_department_chief_flag;

        // Đếm vi phạm trước ngày hiện tại để xác định vị trí vi phạm
        $currentViolationNumber = $this->getViolationCountBeforeDate($user->id, $timesheet->date);

        // Kiểm tra và tính phạt đi muộn
        if ($checkin->gt($expectedCheckin)) {
            $lateMinutes = $checkin->diffInSeconds($expectedCheckin) / 60; // Sử dụng seconds để có độ chính xác cao hơn

            // Kiểm tra có giải trình đi muộn được duyệt không
            // Bao gồm 'insufficient_hours' vì thiếu giờ có thể do đi muộn
            $hasLateExplanation = $this->hasApprovedExplanation($user->id, $timesheet->date, ['late', 'no_checkin', 'insufficient_hours', 'other']);

            if (!$hasLateExplanation) {
                $currentViolationNumber++; // Đây là vi phạm thứ N

                // Logic: Nếu đi muộn >= 120 phút thì không tính phạt tiền
                if ($lateMinutes < 120) {
                    $latePenalty = $this->calculatePenaltyAmount($lateMinutes, $isChief);

                    // Áp dụng x2 nếu đây là vi phạm thứ 7 trở đi
                    if ($currentViolationNumber >= 7) {
                        $latePenalty *= 2;
                    }

                    $totalPenalty += $latePenalty;
                }
                // Nếu >= 120 phút: có violation nhưng không có penalty tiền
            }
        }

        // Kiểm tra và tính phạt về sớm
        if ($checkout->lt($expectedCheckout)) {
            $earlyMinutes = $expectedCheckout->diffInSeconds($checkout) / 60; // Sử dụng seconds để có độ chính xác cao hơn

            // Kiểm tra có giải trình về sớm được duyệt không
            // Bao gồm 'insufficient_hours' vì thiếu giờ có thể do về sớm
            $hasEarlyExplanation = $this->hasApprovedExplanation($user->id, $timesheet->date, ['early', 'no_checkout', 'insufficient_hours', 'other']);

            if (!$hasEarlyExplanation) {
                $currentViolationNumber++; // Đây là vi phạm thứ N+1
                $earlyPenalty = $this->calculatePenaltyAmount($earlyMinutes, $isChief);

                // Áp dụng x2 nếu đây là vi phạm thứ 7 trở đi
                if ($currentViolationNumber >= 7) {
                    $earlyPenalty *= 2;
                }

                $totalPenalty += $earlyPenalty;
            }
        }

        return $totalPenalty;
    }

    /**
     * Kiểm tra có giải trình được duyệt cho các loại vi phạm cụ thể
     */
    private function hasApprovedExplanation($userId, $date, $explanationTypes)
    {
        return AttendanceExplanation::where('user_id', $userId)
            ->whereDate('date', $date)
            ->whereIn('explanation_type', $explanationTypes)
            ->where('final_status', 'approved')
            ->exists();
    }

    /**
     * Tính tiền phạt theo bảng phạt và cấp bậc (bảng phạt chính thức)
     */
    private function calculatePenaltyAmount($minutes, $isChief)
    {
        if ($minutes < 1) {
            return 0;
        }

        // Bảng phạt cho Nhân viên
        if (!$isChief) {
            if ($minutes >= 1 && $minutes <= 10) return 50000;
            if ($minutes >= 11 && $minutes <= 30) return 100000;
            if ($minutes >= 31 && $minutes <= 60) return 120000;
            if ($minutes >= 61 && $minutes <= 120) return 200000;
            if ($minutes > 120) return 0; // Không phạt tiền cho >120 phút
        }
        // Bảng phạt cho Trưởng nhóm
        else {
            if ($minutes >= 1 && $minutes <= 10) return 50000;
            if ($minutes >= 11 && $minutes <= 30) return 100000;
            if ($minutes >= 31 && $minutes <= 60) return 150000;
            if ($minutes >= 61 && $minutes <= 120) return 250000;
            if ($minutes > 120) return 0; // Không phạt tiền cho >120 phút
        }

        return 0;
    }

    /**
     * Đếm tổng số lần vi phạm trong tháng (bao gồm cả vi phạm không phạt tiền)
     */
    private function getMonthlyViolationCount($userId, $date)
    {
        $startOfMonth = Carbon::parse($date)->startOfMonth();
        $endOfMonth = Carbon::parse($date)->endOfMonth();

        // Lấy tất cả timesheet trong tháng với shift_rule_histories data
        $timesheetRepository = app(\App\Contracts\Repositories\TimesheetRepositoryInterface::class);
        $timesheets = $timesheetRepository->queryToExport([
            'user_ids' => [$userId],
            'from_date' => $startOfMonth->format('Y-m-d'),
            'to_date' => $endOfMonth->format('Y-m-d'),
        ])->whereNotNull('checkin')
         ->whereNotNull('checkout')
         ->get();

        $totalViolationCount = 0;

        foreach ($timesheets as $timesheet) {
            // Kiểm tra có giải trình được duyệt không
            $hasApprovedExplanation = $this->hasApprovedExplanation($userId, $timesheet->date, ['late', 'early', 'no_checkin', 'no_checkout', 'insufficient_hours', 'other']);

            // Nếu có giải trình được duyệt thì bỏ qua
            if ($hasApprovedExplanation) {
                continue;
            }

            // Đếm tất cả vi phạm (cả phạt tiền và không phạt tiền)
            $violationCount = $this->countAllViolations($timesheet);
            $totalViolationCount += $violationCount;
        }

        return $totalViolationCount;
    }

    /**
     * Đếm số vi phạm trước ngày hiện tại trong tháng
     * Dùng để xác định vị trí vi phạm (1-7 hay 8+) cho logic x2
     */
    private function getViolationCountBeforeDate($userId, $currentDate)
    {
        $startOfMonth = Carbon::parse($currentDate)->startOfMonth();
        $beforeCurrentDate = Carbon::parse($currentDate)->subDay();

        // Sử dụng repository để có đầy đủ shift_rule_histories data
        $timesheetRepository = app(\App\Contracts\Repositories\TimesheetRepositoryInterface::class);
        $timesheets = $timesheetRepository->queryToExport([
            'user_ids' => [$userId],
            'from_date' => $startOfMonth->format('Y-m-d'),
            'to_date' => $beforeCurrentDate->format('Y-m-d'),
        ])->whereNotNull('checkin')
          ->whereNotNull('checkout')
          ->get();

        $totalViolationCount = 0;

        foreach ($timesheets as $timesheet) {
            // Kiểm tra có giải trình được duyệt không
            $hasApprovedExplanation = AttendanceExplanation::where('user_id', $userId)
                ->whereDate('date', $timesheet->date)
                ->where('final_status', 'approved')
                ->exists();

            // Nếu có giải trình được duyệt thì bỏ qua
            if ($hasApprovedExplanation) {
                continue;
            }

            // Đếm tất cả vi phạm (cả phạt tiền và không phạt tiền)
            $violationCount = $this->countAllViolations($timesheet);
            $totalViolationCount += $violationCount;
        }

        return $totalViolationCount;
    }

    /**
     * Đếm tất cả vi phạm trong một ngày (cả phạt tiền và không phạt tiền)
     */
    private function countAllViolations($timesheet)
    {
        if (!$timesheet->shift_start_time || !$timesheet->shift_end_time || !$timesheet->checkin || !$timesheet->checkout) {
            return 0;
        }

        $checkin = Carbon::parse($timesheet->checkin);
        $checkout = Carbon::parse($timesheet->checkout);
        $timesheetDate = Carbon::parse($timesheet->date);

        $expectedCheckin = $timesheetDate->copy()->setTimeFromTimeString($timesheet->shift_start_time);
        $expectedCheckout = $timesheetDate->copy()->setTimeFromTimeString($timesheet->shift_end_time);

        $violationCount = 0;

        // Đếm vi phạm đi muộn (tất cả mức độ)
        if ($checkin->gt($expectedCheckin)) {
            $violationCount++;
        }

        // Đếm vi phạm về sớm (tất cả mức độ)
        if ($checkout->lt($expectedCheckout)) {
            $violationCount++;
        }

        return $violationCount;
    }

    /**
     * Kiểm tra vi phạm có bị phạt tiền không (áp dụng logic 120 phút)
     */
    private function hasMonetaryPenaltyViolation($timesheet)
    {
        // Kiểm tra vi phạm quên checkin/checkout
        if (!$timesheet->checkin || !$timesheet->checkout) {
            return $this->hasMissingCheckinCheckoutViolation($timesheet);
        }

        $checkin = Carbon::parse($timesheet->checkin);
        $checkout = Carbon::parse($timesheet->checkout);
        $timesheetDate = Carbon::parse($timesheet->date);

        // Lấy thời gian ca làm việc từ query (đã ưu tiên shift_rule_histories)
        if (!$timesheet->shift_start_time || !$timesheet->shift_end_time) {
            return false;
        }

        $expectedCheckin = $timesheetDate->copy()->setTimeFromTimeString($timesheet->shift_start_time);
        $expectedCheckout = $timesheetDate->copy()->setTimeFromTimeString($timesheet->shift_end_time);

        $hasMonetaryPenalty = false;

        // Kiểm tra đi muộn
        if ($checkin->gt($expectedCheckin)) {
            $lateMinutes = $checkin->diffInMinutes($expectedCheckin);

            // Logic mới: Chỉ tính phạt nếu đi muộn < 120 phút
            if ($lateMinutes < 120) {
                // Kiểm tra có giải trình được duyệt không
                $hasLateExplanation = $this->hasApprovedExplanation($timesheet->user_id, $timesheet->date, ['late', 'no_checkin', 'insufficient_hours', 'other']);
                if (!$hasLateExplanation) {
                    $hasMonetaryPenalty = true;
                }
            }
        }

        // Kiểm tra về sớm
        if ($checkout->lt($expectedCheckout)) {
            $earlyMinutes = $expectedCheckout->diffInMinutes($checkout);

            // Kiểm tra có giải trình được duyệt không
            $hasEarlyExplanation = $this->hasApprovedExplanation($timesheet->user_id, $timesheet->date, ['early', 'no_checkout', 'insufficient_hours', 'other']);
            if (!$hasEarlyExplanation) {
                $hasMonetaryPenalty = true;
            }
        }

        return $hasMonetaryPenalty;
    }

    /**
     * Kiểm tra timesheet có vi phạm không (không có giải trình được duyệt)
     */
    private function hasViolation($timesheet)
    {
        // Kiểm tra vi phạm quên checkin/checkout
        if (!$timesheet->checkin || !$timesheet->checkout) {
            return $this->hasMissingCheckinCheckoutViolation($timesheet);
        }

        $checkin = Carbon::parse($timesheet->checkin);
        $checkout = Carbon::parse($timesheet->checkout);
        $timesheetDate = Carbon::parse($timesheet->date);

        // Lấy thời gian ca làm việc từ query (đã ưu tiên shift_rule_histories)
        if (!$timesheet->shift_start_time || !$timesheet->shift_end_time) {
            return false;
        }

        $expectedCheckin = $timesheetDate->copy()->setTimeFromTimeString($timesheet->shift_start_time);
        $expectedCheckout = $timesheetDate->copy()->setTimeFromTimeString($timesheet->shift_end_time);

        $hasViolation = false;

        // Kiểm tra đi muộn
        if ($checkin->gt($expectedCheckin)) {
            // Bao gồm 'insufficient_hours' vì thiếu giờ có thể do đi muộn
            $hasLateExplanation = $this->hasApprovedExplanation($timesheet->user_id, $timesheet->date, ['late', 'no_checkin', 'insufficient_hours', 'other']);
            if (!$hasLateExplanation) {
                $hasViolation = true;
            }
        }

        // Kiểm tra về sớm
        if ($checkout->lt($expectedCheckout)) {
            // Bao gồm 'insufficient_hours' vì thiếu giờ có thể do về sớm
            $hasEarlyExplanation = $this->hasApprovedExplanation($timesheet->user_id, $timesheet->date, ['early', 'no_checkout', 'insufficient_hours', 'other']);
            if (!$hasEarlyExplanation) {
                $hasViolation = true;
            }
        }

        return $hasViolation;
    }

    /**
     * Kiểm tra vi phạm quên checkin/checkout
     */
    private function hasMissingCheckinCheckoutViolation($timesheet)
    {
        $missingCheckin = !$timesheet->checkin;
        $missingCheckout = !$timesheet->checkout;

        // Kiểm tra có giải trình cho vi phạm quên checkin/checkout không
        if ($missingCheckin) {
            $hasNoCheckinExplanation = $this->hasApprovedExplanation($timesheet->user_id, $timesheet->date, ['no_checkin', 'other']);
            if (!$hasNoCheckinExplanation) {
                return true; // Có vi phạm quên checkin
            }
        }

        if ($missingCheckout) {
            $hasNoCheckoutExplanation = $this->hasApprovedExplanation($timesheet->user_id, $timesheet->date, ['no_checkout', 'other']);
            if (!$hasNoCheckoutExplanation) {
                return true; // Có vi phạm quên checkout
            }
        }

        return false; // Không có vi phạm (có giải trình được duyệt)
    }

    // Removed userHasViolations method - summary export không cần check violations

    public function headings(): array
    {
        return [
            'Mã Nhân Viên',
            'Họ và Tên',
            'Phòng ban',
            'Ngày vào',
            'Ngày kết thúc thử việc',
            'Ngày nghỉ việc',
            'Công thử việc',
            'Công chính thức',
            'Ngày Lễ Hưởng Lương',
            'CÔNG ĐI LÀM NGÀY LỄ',
            'CÔNG LÀM ONLINE',
            'CÔNG THỰC TẾ',
            'TỔNG CÔNG',
            'ĐI LÀM NGÀY CHỦ NHẬT',
            'Tổng phạt',
            'Tăng ca',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Header styling
        $sheet->getStyle('A1:P1')->applyFromArray([
            'font' => ['bold' => true, 'size' => 11],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'D9E2F3']
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
                'wrapText' => true
            ],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN]
            ]
        ]);

        // Data styling
        $lastRow = $sheet->getHighestRow();
        $sheet->getStyle('A2:P' . $lastRow)->applyFromArray([
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN]
            ],
            'alignment' => [
                'vertical' => Alignment::VERTICAL_CENTER
            ]
        ]);

        // Number columns alignment
        $sheet->getStyle('G2:P' . $lastRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        // Summary export không có viền đỏ

        return [];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 12, // Mã NV
            'B' => 20, // Họ tên
            'C' => 15, // Phòng ban
            'D' => 12, // Ngày vào
            'E' => 15, // Ngày KT thử việc
            'F' => 12, // Ngày nghỉ việc
            'G' => 12, // Công thử việc
            'H' => 12, // Công chính thức
            'I' => 20, // Ngày lễ
            'J' => 15, // Công đi làm ngày lễ
            'K' => 15, // Công online
            'L' => 12, // Công thực tế
            'M' => 12, // Tổng công
            'N' => 15, // Chủ nhật
            'O' => 15, // Tổng phạt
            'P' => 10, // Tăng ca
        ];
    }

    public function title(): string
    {
        return 'Báo cáo tổng hợp công';
    }
}
