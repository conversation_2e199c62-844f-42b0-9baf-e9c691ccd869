<?php

namespace App\Exports\Timesheet\TimesheetSheet;

use App\Services\Timesheet\ShiftLogicType\LogicTypeFactory;
use App\Models\AttendanceExplanation;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use PhpOffice\PhpSpreadsheet\Calculation\DateTimeExcel\Time;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Generator;

trait TimesheetRows
{
    protected int $timesheetRowsCount = 0;
    protected array $violationRows = []; // Track rows with violations

    public function startCell(): string
    {
        return 'A3';
    }

    public function generator(): Generator
    {
        $timesheetGroupByUsers = $this->getExportQuery()
            ->get()
            ->groupBy('user_id');

        $fromDate = Carbon::createFromFormat(
            config('common.datetime.format.database.date'),
            $this->timesheetConditions['from_date']
        );
        $toDate = Carbon::createFromFormat(
            config('common.datetime.format.database.date'),
            $this->timesheetConditions['to_date']
        );
        $diffInDays = $toDate->diffInDays($fromDate);

        // Lấy tất cả giải trình được duyệt trong khoảng thời gian với filter user_ids
        $explanationQuery = \App\Models\AttendanceExplanation::whereBetween('date', [$fromDate, $toDate])
            ->where('final_status', 'approved');

        // Áp dụng filter user_ids nếu có
        if (isset($this->timesheetConditions['user_ids'])) {
            $explanationQuery->whereIn('user_id', $this->timesheetConditions['user_ids']);
        }

        $approvedExplanations = $explanationQuery->get()->groupBy('user_id');

        $identityUserNumber = 0;
        $this->violationRows = []; // Reset violation rows
        $currentRowIndex = 3; // Start from row 3 (after headers)

        // Lấy tất cả user_ids từ cả timesheets và explanations
        $allUserIds = collect($timesheetGroupByUsers->keys())
            ->merge($approvedExplanations->keys())
            ->unique()
            ->sort();

        foreach ($allUserIds as $userId) {
            $identityUserNumber++;

            // Reset vi phạm lũy tiến cho mỗi user
            $cumulativeViolationCount = 0;

            $timesheetGroupByUser = $timesheetGroupByUsers->get($userId, collect());
            $userExplanations = $approvedExplanations->get($userId, collect());

            // Lấy thông tin user từ timesheet hoặc explanation
            $firstUserTimesheet = $timesheetGroupByUser->first();
            $firstUserExplanation = $userExplanations->first();

            if (!$firstUserTimesheet && !$firstUserExplanation) {
                continue;
            }

            // Lấy thông tin user
            $userCode = $firstUserTimesheet->user_code ?? $firstUserExplanation->user->code ?? '';
            $userName = $firstUserTimesheet->user_name ?? $firstUserExplanation->user->name ?? '';
            $userDepartment = $firstUserTimesheet->user_department ?? $firstUserExplanation->user->staffDepartment->name ?? '';
            $userDepartmentChiefFlag = $firstUserTimesheet->user_department_chief_flag ?? $firstUserExplanation->user->staff_department_chief_flag ?? 0;

            for ($day = 0; $day <= $diffInDays; $day++) {
                $currentDate = $fromDate->copy()->addDays($day)->startOfDay();
                $identityNumber = $day === 0 ? $identityUserNumber : null;

                $dayUserTimesheet = $timesheetGroupByUser
                    ->filter(function ($timesheet) use ($currentDate) {
                        return $currentDate->eq($timesheet->date);
                    })->first();

                $dayUserExplanation = $userExplanations
                    ->filter(function ($explanation) use ($currentDate) {
                        return $currentDate->eq(Carbon::parse($explanation->date));
                    })->first();

                if ($dayUserTimesheet) {
                    $rowData = $this->mapRow($identityNumber, $dayUserTimesheet, $cumulativeViolationCount);

                    // Cập nhật vi phạm lũy tiến nếu có vi phạm trong ngày này
                    if (isset($rowData['daily_violation_count']) && $rowData['daily_violation_count'] > 0) {
                        $cumulativeViolationCount += $rowData['daily_violation_count'];
                    }

                    // Kiểm tra row có vi phạm không
                    if ($this->rowHasViolation($rowData, $dayUserTimesheet)) {
                        $this->violationRows[] = $currentRowIndex;
                    }

                    yield $rowData;
                } elseif ($dayUserExplanation) {
                    // Có giải trình nhưng không có timesheet - tạo row từ giải trình
                    yield $this->mapExplanationRow(
                        $identityNumber,
                        $userCode,
                        $userName,
                        $userDepartment,
                        $userDepartmentChiefFlag,
                        $currentDate,
                        $dayUserExplanation
                    );
                } else {
                    yield $this->mapEmptyRow(
                        $identityNumber,
                        $userCode,
                        $userName,
                        $userDepartment,
                        $userDepartmentChiefFlag,
                        $currentDate
                    );
                }

                $currentRowIndex++;

                $this->timesheetRowsCount++;
            }
        }
    }

    protected function getExportQuery(): Builder
    {
        return $this->timesheetRepository->queryToExport($this->timesheetConditions);
    }

    public function mapRow($identityUserNumberDisplay, $timesheet, $cumulativeViolationCount = 0): array
    {
        // Lấy giải trình cho ngày này
        $explanations = $this->getAttendanceExplanations($timesheet->user_id, $timesheet->date);

        // Lấy tất cả timesheets cho ngày này để xử lý multiple checkin/checkout
        $allTimesheetsForDay = $this->getAllTimesheetsForDay($timesheet->user_id, $timesheet->date);

        // Tính toán dữ liệu chấm công với logic multiple timesheets
        $attendanceData = $this->calculateMultipleTimesheetsAttendance($allTimesheetsForDay, $explanations);

        // Tính penalty cho cả single và multiple timesheets
        if ($allTimesheetsForDay->count() > 1) {
            $attendanceData = $this->calculateMultipleTimesheetsPenalty($attendanceData, $allTimesheetsForDay, $explanations, $this->getPositionValue($timesheet->user_department_chief_flag));
        } else {
            // Single timesheet - tính penalty
            $attendanceData = $this->calculateSingleTimesheetPenalty($attendanceData, $timesheet, $explanations, $this->getPositionValue($timesheet->user_department_chief_flag));
        }

        return $this->map(
            $identityUserNumberDisplay,
            $timesheet->user_code,
            $timesheet->user_name,
            $timesheet->user_department,
            $this->getPositionValue($timesheet->user_department_chief_flag),
            $timesheet->date,
            $this->getCheckinValue($attendanceData['smart_checkin']),
            $this->getCheckoutValue($attendanceData['smart_checkout']),
            $attendanceData['final_workday'],
            $timesheet->shift_name,
            $timesheet,
            $explanations,
            $attendanceData,
            $cumulativeViolationCount
        );
    }

    public function mapEmptyRow(
        $identityUserNumberDisplay,
        $userCode,
        $userName,
        $userDepartment,
        $userStaffDepartmentChiefFlag,
        Carbon $date
    ): array {
        return $this->map(
            $identityUserNumberDisplay,
            $userCode,
            $userName,
            $userDepartment,
            $this->getPositionValue($userStaffDepartmentChiefFlag),
            $date
        );
    }

    public function mapExplanationRow(
        $identityUserNumberDisplay,
        $userCode,
        $userName,
        $userDepartment,
        $userStaffDepartmentChiefFlag,
        Carbon $date,
        $explanation
    ): array {
        // Tạo attendance data từ giải trình
        $attendanceData = [
            'workday' => 0,
            'overtime_hours' => 0,
            'late_arrival_minutes' => 0,
            'early_departure_minutes' => 0,
            'late_arrival_penalty' => 0,
            'early_departure_penalty' => 0,
            'total_penalty' => 0,
            'attendance_status' => '',
            'explanation' => $explanation->explanation ?? '',
            'shift_mismatch_warning' => false,
        ];

        // Xử lý theo loại giải trình
        switch ($explanation->explanation_type) {
            case 'overtime':
                $attendanceData['overtime_hours'] = floatval($explanation->ot_hours ?? 0);
                $attendanceData['workday'] = 0; // Giải trình OT không có timesheet = không tính công
                $attendanceData['attendance_status'] = 'Chỉ OT';
                break;
            case 'remote_work':
                // Lấy workday từ remote_shift_id
                $workday = $this->getWorkdayFromShift($explanation->remote_shift_id);
                $attendanceData['workday'] = $workday;
                $attendanceData['attendance_status'] = 'Làm việc từ xa';
                break;
            case 'shift_change':
                // Giải trình đổi ca không có timesheet = không thể đổi ca
                $attendanceData['workday'] = 0;
                $attendanceData['attendance_status'] = 'Giải trình đổi ca';
                break;
            default:
                // Giải trình khác (đi muộn, về sớm, quên checkin/checkout) - giả định ca HC
                $attendanceData['workday'] = 1;
                $attendanceData['attendance_status'] = 'Có giải trình';
                break;
        }

        return $this->map(
            $identityUserNumberDisplay,
            $userCode,
            $userName,
            $userDepartment,
            $this->getPositionValue($userStaffDepartmentChiefFlag),
            $date,
            null, // checkin
            null, // checkout
            $attendanceData['workday'], // workdayValue
            $attendanceData['attendance_status'], // shiftName (tên ca sẽ hiển thị status)
            null, // timesheet
            collect([$explanation]), // explanations
            null, // multipleTimesheetsData - không dùng cho explanation-only
            0 // cumulativeViolationCount
        );
    }

    public function map(
        $identityUserNumberDisplay,
        $userCode,
        $userName,
        $userDepartment,
        $userStaffDepartmentChiefFlag,
        Carbon $date,
        $checkin = null,
        $checkout = null,
        $workdayValue = 0,
        $shiftName = null,
        $timesheet = null,
        $explanations = null,
        $multipleTimesheetsData = null,
        $cumulativeViolationCount = 0
    ): array {
        // Sử dụng dữ liệu từ multiple timesheets nếu có, nếu không thì tính toán từ single timesheet
        if ($multipleTimesheetsData) {
            $attendanceData = $this->calculateAttendanceDataFromMultiple($multipleTimesheetsData, $explanations, $userStaffDepartmentChiefFlag);
        } else {
            // Tính toán dữ liệu từ giải trình và vi phạm (logic cũ)
            $attendanceData = $this->calculateAttendanceData($timesheet, $explanations, $userStaffDepartmentChiefFlag);
        }

        // Tính vi phạm lũy tiến: vi phạm trước đó + vi phạm trong ngày này
        $dailyViolationCount = $attendanceData['violation_count'] ?? 0;
        $cumulativeViolationTotal = $cumulativeViolationCount + $dailyViolationCount;

        // Hiển thị violation count: ngày không lỗi = 0, ngày có lỗi = cumulative total
        $displayViolationCount = $dailyViolationCount > 0 ? $cumulativeViolationTotal : 0;

        // Lưu vi phạm trong ngày để cập nhật cumulative count
        $attendanceData['daily_violation_count'] = $dailyViolationCount;

        return [
            $identityUserNumberDisplay,
            $userCode,
            $userName,
            $userDepartment,
            $userStaffDepartmentChiefFlag,
            Date::dateTimeToExcel($date),
            get_day_of_weeks($date),
            $checkin,
            $checkout,
            $workdayValue,
            $shiftName,
            $this->getVietnameseStatus($attendanceData['attendance_status']), // Xác nhận tình trạng chấm công (hiển thị tiếng Việt)
            $attendanceData['explanation_summary'],         // Giải trình công
            $attendanceData['final_workday'],              // Công (sau điều chỉnh)
            $attendanceData['late_minutes'],               // Số phút đi muộn
            $attendanceData['late_explanation'],           // GT đi muộn
            $attendanceData['late_penalty'],               // Phạt đi muộn (số tiền gốc)
            $attendanceData['early_minutes'],              // Số phút về sớm
            $attendanceData['early_explanation'],          // GT về sớm
            $attendanceData['early_penalty'],              // Phạt về sớm (số tiền gốc)
            $displayViolationCount,                        // Số lần vi phạm (0 nếu không lỗi, cumulative nếu có lỗi)
            $attendanceData['total_penalty_with_multiplier'] ?? $attendanceData['total_penalty'], // Tổng tiền phạt (đã x2)
            $attendanceData['penalty_workday'],            // Phạt công
            $attendanceData['total_workday'],              // Tổng công
            $attendanceData['overtime_hours'],             // Tăng ca
            $attendanceData['rule_start_time'],            // AA: Giờ bắt đầu (từ rule hoặc shift)
            $attendanceData['rule_end_time'],              // AB: Giờ kết thúc (từ rule hoặc shift)
            $attendanceData['rule_break_times'],           // AC: Giờ nghỉ (từ rule hoặc shift)
            'daily_violation_count' => $dailyViolationCount, // Hidden field for cumulative calculation
        ];
    }

    protected function getPositionValue($isChief): string
    {
        $configKey = $isChief
            ? 'department_chief_label'
            : 'staff_label';

        return config('export.staff_timesheets.' . $configKey);
    }

    protected function getCheckinValue($checkin)
    {
        if (!$checkin) {
            return null;
        }

        return Time::fromHMS(
            $checkin->format('H'),
            $checkin->format('i'),
            $checkin->format('s')
        );
    }

    protected function getCheckoutValue($checkout)
    {
        if (!$checkout) {
            return null;
        }

        return Time::fromHMS(
            $checkout->format('H'),
            $checkout->format('i'),
            $checkout->format('s')
        );
    }

    protected function getWorkDayValue($timesheet): float
    {
        // Kiểm tra logic 120 phút cho vi phạm trước khi tính workday
        if ($timesheet->checkin && $timesheet->checkout && $timesheet->shift_start_time && $timesheet->shift_end_time) {
            $checkin = \Carbon\Carbon::parse($timesheet->checkin);
            $checkout = \Carbon\Carbon::parse($timesheet->checkout);
            $dateString = $checkin->format('Y-m-d');
            $expectedCheckin = \Carbon\Carbon::parse($dateString . ' ' . $timesheet->shift_start_time);
            $expectedCheckout = \Carbon\Carbon::parse($dateString . ' ' . $timesheet->shift_end_time);

            // Thêm tolerance 60 giây để tránh cảnh báo "chọn sai ca làm việc"
            $lateSeconds = $checkin->gt($expectedCheckin) ? $checkin->diffInSeconds($expectedCheckin) : 0;
            $earlySeconds = $checkout->lt($expectedCheckout) ? $expectedCheckout->diffInSeconds($checkout) : 0;

            $lateMinutes = $lateSeconds > 60 ? $lateSeconds / 60 : 0;
            $earlyMinutes = $earlySeconds > 60 ? $earlySeconds / 60 : 0;

            // Logic mới: Nếu vi phạm >= 120 phút thì tính công bình thường (có thể bị trừ)
            $hasLargeLateViolation = $lateMinutes >= 120;
            $hasLargeEarlyViolation = $earlyMinutes >= 120;

            if ($hasLargeLateViolation || $hasLargeEarlyViolation) {
                // Vi phạm >= 120 phút: tính công bình thường sử dụng LogicTypeFactory
                if (!$timesheet->checkin || !$timesheet->checkout) {
                    return 0;
                }

                return LogicTypeFactory::create()->calcWorkday([
                    'schedule_start_time' => $timesheet->shift_start_time,
                    'schedule_end_time' => $timesheet->shift_end_time,
                    'start_time' => $timesheet->checkin?->format('H:i:s'),
                    'end_time' => $timesheet->checkout?->format('H:i:s'),
                    'workday_min_1' => $timesheet->shift_workday_min_1,
                    'workday_min_2' => $timesheet->shift_workday_min_2,
                    'break_times' => $this->parseBreakTimes($timesheet->shift_break_times),
                ]);
            }

            // Vi phạm < 120 phút hoặc không vi phạm: force đủ công
            return $timesheet->shift_default_workday_threshold ?? 1.0; // Sử dụng threshold từ shift
        }

        // Logic cũ: Nếu có multiple timesheets thì workday = 0
        // Logic mới: Sử dụng smart checkin/checkout để tính workday
        // Parse break times from database
        $breakTimes = $this->parseBreakTimes($timesheet->shift_break_times);

        if ($timesheet->timsheets_unique_date_user_id_count > 1) {
            // Với multiple timesheets, sử dụng smart checkin/checkout times
            // Checkin và checkout trong $timesheet đã là MIN(checkin) và MAX(checkout) từ query
            return LogicTypeFactory::create()->calcWorkday([
                'schedule_start_time' => $timesheet->shift_start_time,
                'schedule_end_time' => $timesheet->shift_end_time,
                'start_time' => $timesheet->checkin?->format('H:i:s'),
                'end_time' => $timesheet->checkout?->format('H:i:s'),
                'workday_min_1' => $timesheet->shift_workday_min_1,
                'workday_min_2' => $timesheet->shift_workday_min_2,
                'break_times' => $breakTimes,
            ]);
        }

        // Single timesheet - logic bình thường
        return LogicTypeFactory::create()->calcWorkday([
            'schedule_start_time' => $timesheet->shift_start_time,
            'schedule_end_time' => $timesheet->shift_end_time,
            'start_time' => $timesheet->checkin?->format('H:i:s'),
            'end_time' => $timesheet->checkout?->format('H:i:s'),
            'workday_min_1' => $timesheet->shift_workday_min_1,
            'workday_min_2' => $timesheet->shift_workday_min_2,
            'break_times' => $breakTimes,
        ]);
    }

    public function columnFormats(): array
    {
        return [
            'F' => NumberFormat::FORMAT_DATE_DDMMYYYY,  // Ngày
            'H' => 'hh:mm',                             // Giờ vào
            'I' => 'hh:mm',                             // Giờ ra
            'J' => '0.0',                               // Công
            'N' => '0.0',                               // Công (sau điều chỉnh)
            'O' => '0',                                 // Số phút đi muộn
            'Q' => '#,##0',                             // Phạt đi muộn
            'R' => '0',                                 // Số phút về sớm
            'T' => '#,##0',                             // Phạt về sớm
            'U' => '0',                                 // Số lần vi phạm
            'V' => '#,##0',                             // Tổng tiền phạt
            'W' => '0.0',                               // Phạt công
            'X' => '0.0',                               // Tổng công
            'Y' => '0.0',                               // Tăng ca
            'Z' => '@',                                 // AA: Giờ bắt đầu (text)
            'AA' => '@',                                // AB: Giờ kết thúc (text)
            'AB' => '@',                                // AC: Giờ nghỉ (text)
        ];
    }

    protected function getTimesheetRowsCount(): int
    {
        return $this->timesheetRowsCount;
    }

    /**
     * Lấy giải trình chấm công cho user và ngày cụ thể
     */
    private function getAttendanceExplanations($userId, $date)
    {
        return AttendanceExplanation::where('user_id', $userId)
            ->whereDate('date', $date)
            ->where('final_status', 'approved')
            ->get();
    }

    /**
     * Lấy tất cả timesheets cho một ngày cụ thể với shift_rule_histories data
     */
    protected function getAllTimesheetsForDay($userId, $date)
    {
        // Sử dụng repository để có đầy đủ shift_rule_histories data
        return $this->timesheetRepository->queryToExport([
            'user_ids' => [$userId],
            'from_date' => $date,
            'to_date' => $date,
        ])->get();
    }

    /**
     * Tính toán dữ liệu chấm công cho multiple timesheets trong một ngày
     */
    protected function calculateMultipleTimesheetsAttendance($timesheets, $explanations)
    {
        if ($timesheets->isEmpty()) {
            return [
                'smart_checkin' => null,
                'smart_checkout' => null,
                'final_workday' => 0,
                'total_real_hours' => 0,
                'attendance_data' => [
                    'attendance_status' => 'absent',
                    'explanation_summary' => '',
                    'final_workday' => 0,
                    'late_minutes' => 0,
                    'late_explanation' => '',
                    'late_penalty' => 0,
                    'early_minutes' => 0,
                    'early_explanation' => '',
                    'early_penalty' => 0,
                    'violation_count' => 0,
                    'total_penalty' => 0,
                    'total_penalty_with_multiplier' => 0,
                ]
            ];
        }

        // Nếu chỉ có 1 timesheet, sử dụng logic calculateAttendanceData
        if ($timesheets->count() === 1) {
            $timesheet = $timesheets->first();

            // Sử dụng calculateAttendanceData để có đầy đủ penalty calculation
            $attendanceData = $this->calculateAttendanceData($timesheet, $explanations, false); // isChief will be set later

            return [
                'smart_checkin' => $timesheet->checkin,
                'smart_checkout' => $timesheet->checkout,
                'final_workday' => $attendanceData['final_workday'],
                'total_real_hours' => 0, // Skip calcWorkDayRealHours() for now
                'attendance_data' => $attendanceData, // Pass full attendance data
            ];
        }

        // Logic cho multiple timesheets
        $smartCheckin = $this->getEarliestCheckin($timesheets);
        $smartCheckout = $this->getLatestValidCheckout($timesheets);

        // Tính tổng workday từ tất cả timesheets sử dụng LogicTypeFactory
        $totalWorkday = $timesheets->sum(function($timesheet) {
            if (!$timesheet->checkin || !$timesheet->checkout) {
                return 0;
            }

            return LogicTypeFactory::create()->calcWorkday([
                'schedule_start_time' => $timesheet->shift_start_time,
                'schedule_end_time' => $timesheet->shift_end_time,
                'start_time' => $timesheet->checkin?->format('H:i:s'),
                'end_time' => $timesheet->checkout?->format('H:i:s'),
                'workday_min_1' => $timesheet->shift_workday_min_1,
                'workday_min_2' => $timesheet->shift_workday_min_2,
                'break_times' => $this->parseBreakTimes($timesheet->shift_break_times),
            ]);
        });

        // Áp dụng điều chỉnh từ giải trình đã duyệt
        $finalWorkday = $this->getFinalWorkdayForMultiple($timesheets, $explanations);

        return [
            'smart_checkin' => $smartCheckin,
            'smart_checkout' => $smartCheckout,
            'final_workday' => $finalWorkday,
            'total_real_hours' => $this->calculateOverallRealHours($timesheets),
        ];
    }

    /**
     * Lấy checkin sớm nhất từ tất cả timesheets
     */
    protected function getEarliestCheckin($timesheets)
    {
        return $timesheets->filter(function($timesheet) {
            return $timesheet->checkin !== null;
        })->min('checkin');
    }

    /**
     * Lấy checkout cuối cùng hợp lệ:
     * - Ưu tiên checkout của bản ghi cuối cùng nếu có
     * - Nếu bản ghi cuối không có checkout, lấy checkout của bản ghi gần cuối nhất có checkout
     */
    protected function getLatestValidCheckout($timesheets)
    {
        // Sắp xếp theo thời gian checkin để có thứ tự đúng
        $sortedTimesheets = $timesheets->sortBy('checkin');

        // Duyệt từ cuối lên đầu để tìm checkout hợp lệ
        for ($i = $sortedTimesheets->count() - 1; $i >= 0; $i--) {
            $timesheet = $sortedTimesheets->values()->get($i);
            if ($timesheet->checkout !== null) {
                return $timesheet->checkout;
            }
        }

        // Nếu không có checkout nào, trả về null
        return null;
    }

    /**
     * Tính toán real_hours tổng thể từ checkin đầu tiên đến checkout cuối cùng, trừ thời gian nghỉ trưa
     */
    protected function calculateOverallRealHours($timesheets): float
    {
        if ($timesheets->isEmpty()) {
            return 0;
        }

        // Lấy checkin sớm nhất và checkout muộn nhất
        $earliestCheckin = $timesheets->filter(function($timesheet) {
            return $timesheet->checkin !== null;
        })->min('checkin');

        $latestCheckout = $timesheets->filter(function($timesheet) {
            return $timesheet->checkout !== null;
        })->max('checkout');

        if (!$earliestCheckin || !$latestCheckout) {
            return 0;
        }

        // Tính tổng thời gian từ checkin đầu đến checkout cuối
        $totalHours = $latestCheckout->floatDiffInHours($earliestCheckin);

        // Sử dụng break time mặc định 1 giờ (12:00-13:00) để đơn giản
        $breakHours = 1.0;

        // Trừ thời gian nghỉ
        $realHours = $totalHours - $breakHours;

        // Đảm bảo không âm
        return max(0, $realHours);
    }

    /**
     * Tính toán số công cuối cùng cho single timesheet sau khi áp dụng điều chỉnh từ giải trình
     */
    protected function getFinalWorkdayForSingle($timesheet, $explanations, $originalWorkday): float
    {
        if (!$explanations || $explanations->isEmpty()) {
            return $originalWorkday; // No explanations = use original workday
        }

        // Lấy các giải trình đã được duyệt và ảnh hưởng đến workday
        $approvedExplanations = $explanations->filter(function($explanation) {
            return $explanation->final_status === 'approved' &&
                   in_array($explanation->explanation_type, [
                       'late', 'early', 'no_checkin', 'no_checkout',
                       'insufficient_hours', 'remote_work', 'shift_change', 'new_employee_no_account', 'other'
                   ]);
        });

        if ($approvedExplanations->isEmpty()) {
            return $originalWorkday; // No approved explanations = use original workday
        }

        // Chỉ điều chỉnh workday cho một số loại giải trình cụ thể
        $workdayAdjustmentTypes = [
            'no_checkin', 'no_checkout', 'insufficient_hours',
            'remote_work', 'shift_change', 'new_employee_no_account'
        ];

        $hasWorkdayAdjustment = $approvedExplanations->whereIn('explanation_type', $workdayAdjustmentTypes)->isNotEmpty();

        if ($hasWorkdayAdjustment) {
            // Xử lý đặc biệt cho shift_change: sử dụng ca từ timesheet (đã được cập nhật)
            $shiftChangeExplanations = $approvedExplanations->where('explanation_type', 'shift_change');
            if ($shiftChangeExplanations->isNotEmpty()) {
                // Timesheet đã được cập nhật với ca mới, sử dụng calcWorkday() để tính chính xác
                return $timesheet->calcWorkday();
            }

            // Các loại giải trình khác: sử dụng threshold từ shift hiện tại
            return $timesheet->shift_default_workday_threshold ?? 1.0;
        }

        // Các loại giải trình khác (late, early, other) không điều chỉnh workday
        return $originalWorkday;
    }

    /**
     * Tính toán số công cuối cùng cho nhiều timesheets sau khi áp dụng điều chỉnh từ giải trình
     */
    protected function getFinalWorkdayForMultiple($timesheets, $explanations): float
    {
        $totalOriginalWorkday = $timesheets->sum(function($timesheet) {
            if (!$timesheet->checkin || !$timesheet->checkout) {
                return 0;
            }

            return LogicTypeFactory::create()->calcWorkday([
                'schedule_start_time' => $timesheet->shift_start_time,
                'schedule_end_time' => $timesheet->shift_end_time,
                'start_time' => $timesheet->checkin?->format('H:i:s'),
                'end_time' => $timesheet->checkout?->format('H:i:s'),
                'workday_min_1' => $timesheet->shift_workday_min_1,
                'workday_min_2' => $timesheet->shift_workday_min_2,
                'break_times' => $this->parseBreakTimes($timesheet->shift_break_times),
            ]);
        });

        // Lấy các giải trình đã được duyệt và ảnh hưởng đến workday
        $approvedExplanations = $explanations->filter(function($explanation) {
            return $explanation->final_status === 'approved' &&
                   in_array($explanation->explanation_type, [
                       'late', 'early', 'no_checkin', 'no_checkout',
                       'insufficient_hours', 'remote_work', 'shift_change', 'new_employee_no_account', 'other'
                   ]);
        });

        if ($approvedExplanations->isEmpty()) {
            return $totalOriginalWorkday;
        }

        // Nếu có giải trình được duyệt, điều chỉnh workday
        // Các loại giải trình này thường được tính là 1 công khi được duyệt
        $hasWorkdayAdjustment = $approvedExplanations->whereIn('explanation_type', [
            'late', 'early', 'no_checkin', 'no_checkout',
            'insufficient_hours', 'remote_work', 'shift_change', 'new_employee_no_account', 'other'
        ])->isNotEmpty();

        if ($hasWorkdayAdjustment) {
            return 1.0; // Giải trình được duyệt = 1 công
        }

        return $totalOriginalWorkday;
    }

    /**
     * Tính toán dữ liệu chấm công từ multiple timesheets data
     */
    private function calculateAttendanceDataFromMultiple($multipleTimesheetsData, $explanations, $isChief)
    {
        // Check if we have attendance_data from single timesheet
        if (isset($multipleTimesheetsData['attendance_data'])) {
            return $multipleTimesheetsData['attendance_data'];
        }

        $data = [
            'attendance_status' => 'normal',
            'explanation_summary' => '',
            'final_workday' => $multipleTimesheetsData['final_workday'],
            'late_minutes' => $multipleTimesheetsData['late_minutes'] ?? 0,
            'late_explanation' => '',
            'late_penalty' => $multipleTimesheetsData['late_penalty'] ?? 0,
            'early_minutes' => $multipleTimesheetsData['early_minutes'] ?? 0,
            'early_explanation' => '',
            'early_penalty' => $multipleTimesheetsData['early_penalty'] ?? 0,
            'violation_count' => $multipleTimesheetsData['violation_count'] ?? 0,
            'total_penalty' => $multipleTimesheetsData['total_penalty'] ?? 0,
            'total_penalty_with_multiplier' => $multipleTimesheetsData['total_penalty'] ?? 0,
            'penalty_workday' => 0,
            'total_workday' => $multipleTimesheetsData['final_workday'],
            'overtime_hours' => 0,
            'rule_start_time' => '',
            'rule_end_time' => '',
            'rule_break_times' => '',
        ];

        // Cập nhật attendance status từ multipleTimesheetsData nếu có
        if (isset($multipleTimesheetsData['attendance_status'])) {
            $data['attendance_status'] = $multipleTimesheetsData['attendance_status'];
        }

        // Xử lý giải trình nếu có
        if ($explanations && $explanations->count() > 0) {
            $data = $this->applyExplanations($data, $explanations, null);
        }

        return $data;
    }

    /**
     * Tính vi phạm từ smart checkin/checkout times
     */
    private function calculateViolationsFromSmartTimes($data, $smartCheckin, $smartCheckout, $explanations, $isChief)
    {
        // Luôn tính violation bất kể workday (vì đi muộn/về sớm vẫn là vi phạm)

        // KHÔNG sử dụng hardcoded shift times nữa
        // Method này chỉ được gọi từ calculateAttendanceDataFromMultiple
        // mà không có thông tin shift, nên không thể tính penalty chính xác
        //
        // Penalty sẽ được tính ở level cao hơn với đầy đủ thông tin shift
        // Tạm thời return data mà không tính penalty

        return $data;
    }

    /**
     * Tính penalty cho single timesheet
     */
    private function calculateSingleTimesheetPenalty($attendanceData, $timesheet, $explanations, $isChief)
    {
        // Luôn tính violation bất kể workday

        if (!$timesheet->checkin || !$timesheet->checkout) {
            return $attendanceData;
        }

        // Lấy thời gian ca làm việc từ shift_rule_histories
        $scheduleStart = $timesheet->shift_start_time;
        $scheduleEnd = $timesheet->shift_end_time;

        if (!$scheduleStart || !$scheduleEnd) {
            return $attendanceData;
        }

        $checkin = \Carbon\Carbon::parse($timesheet->checkin);
        $checkout = \Carbon\Carbon::parse($timesheet->checkout);
        $dateString = $checkin->format('Y-m-d');

        $expectedCheckin = \Carbon\Carbon::parse($dateString . ' ' . $scheduleStart);
        $expectedCheckout = \Carbon\Carbon::parse($dateString . ' ' . $scheduleEnd);

        // Tính vi phạm với tolerance 60 giây
        $lateSeconds = $checkin->gt($expectedCheckin) ? $checkin->diffInSeconds($expectedCheckin) : 0;
        $earlySeconds = $checkout->lt($expectedCheckout) ? $expectedCheckout->diffInSeconds($checkout) : 0;

        $lateMinutes = $lateSeconds > 60 ? $lateSeconds / 60 : 0;
        $earlyMinutes = $earlySeconds > 60 ? $earlySeconds / 60 : 0;

        // Tính penalty
        $latePenalty = 0;
        $earlyPenalty = 0;
        $violationCount = 0;

        if ($lateMinutes > 0) {
            $hasLateExplanation = $this->hasExplanationForType($explanations, ['late', 'other']);
            if (!$hasLateExplanation) {
                $violationCount++; // Luôn tính violation cho đi muộn
                // Logic mới: Nếu đi muộn >= 120 phút thì không tính phạt tiền
                if ($lateMinutes < 120) {
                    $latePenalty = $this->calculatePenaltyAmount($lateMinutes, $isChief);
                }
                // Nếu >= 120 phút: có violation nhưng không có penalty tiền
            }
        }

        if ($earlyMinutes > 0) {
            $hasEarlyExplanation = $this->hasExplanationForType($explanations, ['early', 'other']);
            if (!$hasEarlyExplanation) {
                $earlyPenalty = $this->calculatePenaltyAmount($earlyMinutes, $isChief);
                $violationCount++;
            }
        }

        // Update attendance data
        $attendanceData['late_minutes'] = $lateMinutes;
        $attendanceData['early_minutes'] = $earlyMinutes;
        $attendanceData['late_penalty'] = $latePenalty;
        $attendanceData['early_penalty'] = $earlyPenalty;
        $attendanceData['total_penalty'] = $latePenalty + $earlyPenalty;
        $attendanceData['total_penalty_with_multiplier'] = $latePenalty + $earlyPenalty;
        $attendanceData['violation_count'] = $violationCount;

        return $attendanceData;
    }

    /**
     * Tính penalty cho multiple timesheets với thông tin shift chính xác
     */
    private function calculateMultipleTimesheetsPenalty($attendanceData, $timesheets, $explanations, $isChief)
    {
        // Luôn tính violation bất kể workday

        // Lấy shift thông tin từ timesheet đầu tiên
        $primaryTimesheet = $timesheets->first();
        $shift = $primaryTimesheet->shift;
        $shiftRule = $primaryTimesheet->shiftRuleHistory;

        if (!$shift) {
            return $attendanceData;
        }

        // Lấy thời gian ca làm việc thực tế
        $scheduleStart = $shiftRule ? $shiftRule->start_time : $shift->start_time;
        $scheduleEnd = $shiftRule ? $shiftRule->end_time : $shift->end_time;

        if (!$scheduleStart || !$scheduleEnd) {
            return $attendanceData;
        }

        // Parse smart times
        $smartCheckin = \Carbon\Carbon::parse($attendanceData['smart_checkin']);
        $smartCheckout = \Carbon\Carbon::parse($attendanceData['smart_checkout']);

        // Parse expected times
        $expectedCheckin = \Carbon\Carbon::parse($primaryTimesheet->date->format('Y-m-d') . ' ' . $scheduleStart);
        $expectedCheckout = \Carbon\Carbon::parse($primaryTimesheet->date->format('Y-m-d') . ' ' . $scheduleEnd);

        $lateMinutes = 0;
        $earlyMinutes = 0;
        $violationCount = 0;
        $latePenalty = 0;
        $earlyPenalty = 0;

        // Kiểm tra đi muộn với tolerance 60 giây
        if ($smartCheckin->gt($expectedCheckin)) {
            $lateSeconds = $smartCheckin->diffInSeconds($expectedCheckin);
            $lateMinutes = $lateSeconds > 60 ? $lateSeconds / 60 : 0;

            // Logic: Nếu đi muộn >= 120 phút thì không tính phạt tiền
            if ($lateMinutes >= 120) {
                // Đi muộn >= 120 phút: có violation nhưng không có penalty tiền
                $hasLateExplanation = $this->hasSpecificExplanation($explanations, ['late', 'no_checkin', 'insufficient_hours', 'other']);
                if (!$hasLateExplanation) {
                    $violationCount++; // Vẫn tính violation
                    // Không tính latePenalty (= 0)
                }
            } else {
                $hasLateExplanation = $this->hasSpecificExplanation($explanations, ['late', 'no_checkin', 'insufficient_hours', 'other']);

                if (!$hasLateExplanation) {
                    $latePenalty = $this->calculatePenaltyAmount($lateMinutes, $isChief);
                    $violationCount++;
                }
            }
        }

        // Kiểm tra về sớm với tolerance 60 giây
        if ($smartCheckout->lt($expectedCheckout)) {
            $earlySeconds = $expectedCheckout->diffInSeconds($smartCheckout);
            $earlyMinutes = $earlySeconds > 60 ? $earlySeconds / 60 : 0;

            $hasEarlyExplanation = $this->hasSpecificExplanation($explanations, ['early', 'no_checkout', 'insufficient_hours', 'other']);

            if (!$hasEarlyExplanation) {
                $earlyPenalty = $this->calculatePenaltyAmount($earlyMinutes, $isChief);
                $violationCount++;
            }
        }

        // Cập nhật attendance data với penalty information
        $attendanceData['late_minutes'] = $lateMinutes;
        $attendanceData['early_minutes'] = $earlyMinutes;
        $attendanceData['late_penalty'] = $latePenalty;
        $attendanceData['early_penalty'] = $earlyPenalty;
        $attendanceData['violation_count'] = $violationCount;
        $attendanceData['total_penalty'] = $latePenalty + $earlyPenalty;
        $attendanceData['total_penalty_with_multiplier'] = $latePenalty + $earlyPenalty;

        // Cập nhật status
        if ($lateMinutes > 0 || $earlyMinutes > 0) {
            $attendanceData['attendance_status'] = 'has_issue';
        }

        // Set attendance status cho các trường hợp có giải trình (nếu chưa có status khác)
        if ($attendanceData['attendance_status'] === 'normal' && !empty($attendanceData['explanation_summary'])) {
            $attendanceData['attendance_status'] = 'has_explanation';
        }

        return $attendanceData;
    }

    /**
     * Kiểm tra có giải trình cho các loại vi phạm cụ thể
     */
    private function hasSpecificExplanation($explanations, $explanationTypes)
    {
        if (!$explanations || $explanations->count() === 0) {
            return false;
        }

        return $explanations->whereIn('explanation_type', $explanationTypes)->count() > 0;
    }

    /**
     * Tính toán dữ liệu chấm công từ timesheet và giải trình
     */
    private function calculateAttendanceData($timesheet, $explanations, $isChief)
    {
        $data = [
            'attendance_status' => 'normal',
            'explanation_summary' => '',
            'final_workday' => 0,
            'late_minutes' => 0,
            'late_explanation' => '',
            'late_penalty' => 0,
            'early_minutes' => 0,
            'early_explanation' => '',
            'early_penalty' => 0,
            'violation_count' => 0,
            'total_penalty' => 0,
            'total_penalty_with_multiplier' => 0,
            'penalty_workday' => 0,
            'total_workday' => 0,
            'overtime_hours' => 0,
            'rule_start_time' => '',
            'rule_end_time' => '',
            'rule_break_times' => '',
        ];

        if (!$timesheet) {
            // Nếu không có timesheet nhưng có explanations, xử lý explanations
            if ($explanations && $explanations->count() > 0) {
                $data = $this->applyExplanationsWithoutTimesheet($data, $explanations);
            } else {
                $data['attendance_status'] = 'absent';
            }
            return $data;
        }

        // Áp dụng shift change nếu có approved explanation
        $effectiveTimesheet = $this->applyShiftChangeIfApproved($timesheet, $explanations);

        // Lấy thông tin ca làm việc từ shift_rule_histories hoặc shifts
        $data = $this->populateShiftRuleData($data, $effectiveTimesheet);

        // Công cơ bản từ timesheet
        $data['final_workday'] = $this->getWorkDayValue($effectiveTimesheet);
        $data['total_workday'] = $data['final_workday'];

        // Xử lý giải trình nếu có
        if ($explanations && $explanations->count() > 0) {
            $data = $this->applyExplanations($data, $explanations, $effectiveTimesheet);
        }

        // Tính toán trạng thái chấm công sử dụng logic mới
        $workday = $this->getWorkDayValue($effectiveTimesheet);
        $lateMinutes = $this->calculateLateMinutes($effectiveTimesheet);
        $earlyMinutes = $this->calculateEarlyMinutes($effectiveTimesheet);

        // Sử dụng logic tương tự AttendanceExplanationService để xác định status
        $attendanceStatus = $this->determineAttendanceStatusUsingService($workday, $lateMinutes, $earlyMinutes, $effectiveTimesheet);

        // Cập nhật attendance status dựa trên logic mới (sử dụng status tiếng Anh)
        if ($attendanceStatus === 'has_issue') {
            $data['attendance_status'] = 'has_issue';
        } elseif ($attendanceStatus === 'shift_mismatch') {
            $data['attendance_status'] = 'shift_mismatch';
        } elseif ($attendanceStatus === 'absent') {
            $data['attendance_status'] = 'absent';
        } elseif ($attendanceStatus === 'incomplete_attendance') {
            $data['attendance_status'] = 'incomplete_attendance';
        } elseif ($attendanceStatus === 'perfect') {
            $data['attendance_status'] = 'normal';
        }

        // Tính vi phạm cho những loại không có giải trình
        $data = $this->calculateViolationsWithExplanationCheck($data, $effectiveTimesheet, $explanations, $isChief);

        // Áp dụng quy tắc x2 nếu vi phạm > 6 lần trong tháng
        if ($data['violation_count'] > 0) {
            $data = $this->applyMonthlyViolationMultiplier($data, $effectiveTimesheet);
        }

        // Set attendance status cho các trường hợp có giải trình (nếu chưa có status khác)
        if ($data['attendance_status'] === 'normal' && !empty($data['explanation_summary'])) {
            $data['attendance_status'] = 'has_explanation';
        }

        return $data;
    }

    /**
     * Tính toán số phút đi muộn
     */
    private function calculateLateMinutes($timesheet): int
    {
        if (!$timesheet->checkin || !$timesheet->shift_start_time) {
            return 0;
        }

        $checkinTime = Carbon::parse($timesheet->checkin);
        $dateString = Carbon::parse($timesheet->date)->format('Y-m-d');
        $scheduleStartTime = Carbon::parse($dateString . ' ' . $timesheet->shift_start_time);

        return $checkinTime->gt($scheduleStartTime) ? $checkinTime->diffInMinutes($scheduleStartTime) : 0;
    }

    /**
     * Tính toán số phút về sớm
     */
    private function calculateEarlyMinutes($timesheet): int
    {
        if (!$timesheet->checkout || !$timesheet->shift_end_time) {
            return 0;
        }

        $checkoutTime = Carbon::parse($timesheet->checkout);
        $dateString = Carbon::parse($timesheet->date)->format('Y-m-d');
        $scheduleEndTime = Carbon::parse($dateString . ' ' . $timesheet->shift_end_time);

        return $checkoutTime->lt($scheduleEndTime) ? $scheduleEndTime->diffInMinutes($checkoutTime) : 0;
    }

    /**
     * Sử dụng logic tương tự AttendanceExplanationService để xác định trạng thái
     * Tái tạo logic để tránh dependency issues
     */
    private function determineAttendanceStatusUsingService(float $workday, int $lateMinutes, int $earlyMinutes, $timesheet): string
    {
        // Kiểm tra trường hợp nghỉ làm (không có checkin và checkout)
        if (!$timesheet->checkin && !$timesheet->checkout) {
            return 'absent';
        }

        // Logic đơn giản: Nếu workday = 0 → Luôn là "Quên chấm công"
        if ($workday == 0) {
            return 'incomplete_attendance';
        }

        // Tính expected workday (logic đơn giản)
        $expectedWorkday = $this->calculateExpectedWorkdayForExport($timesheet);

        // Kiểm tra các trường hợp có vấn đề
        if ($lateMinutes > 0 || $earlyMinutes > 0 || $workday < $expectedWorkday) {
            return 'has_issue';
        }

        // Kiểm tra shift mismatch - cảnh báo ca làm việc không phù hợp
        $shiftMismatchWarning = $this->checkShiftMismatchForExport($timesheet, null);
        if ($shiftMismatchWarning) {
            return 'shift_mismatch'; // Trả về status đặc biệt cho shift mismatch
        }

        // Chấm công hoàn hảo
        return 'perfect';
    }

    /**
     * Kiểm tra incomplete attendance cho export (logic từ AttendanceExplanationService)
     */
    private function isIncompleteAttendanceForExport($timesheet, float $workday): bool
    {
        // Case 1: Thiếu checkin hoặc checkout
        if (!$timesheet->checkin || !$timesheet->checkout) {
            return true;
        }

        // Case 2: Checkin và checkout giống nhau (quên chấm công)
        if ($timesheet->checkin && $timesheet->checkout) {
            $checkinTime = Carbon::parse($timesheet->checkin);
            $checkoutTime = Carbon::parse($timesheet->checkout);

            // Nếu checkin = checkout (cùng thời điểm) thì là quên chấm công
            if ($checkinTime->equalTo($checkoutTime)) {
                return true;
            }
        }

        // Case 3: Có checkin và checkout nhưng công số bằng 0
        // Chỉ kiểm tra khi có cả checkin và checkout
        if ($timesheet->checkin && $timesheet->checkout && $workday == 0) {
            return true;
        }

        return false;
    }

    /**
     * Tính expected workday cho export (logic đơn giản)
     */
    private function calculateExpectedWorkdayForExport($timesheet): float
    {
        // Sử dụng shift_default_workday_threshold từ query export
        if (isset($timesheet->shift_default_workday_threshold) && $timesheet->shift_default_workday_threshold !== null) {
            return (float) $timesheet->shift_default_workday_threshold;
        }

        // Fallback: nếu có shift relationship thì dùng default_workday_threshold
        if (isset($timesheet->shift) && $timesheet->shift && isset($timesheet->shift->default_workday_threshold)) {
            return $timesheet->shift->default_workday_threshold;
        }

        return 0.8; // Fallback default
    }

    /**
     * Kiểm tra shift mismatch - cảnh báo ca làm việc không phù hợp
     */
    private function checkShiftMismatchForExport($timesheet, $explanations): ?string
    {
        if (!$timesheet->checkin || !$timesheet->checkout) {
            return null;
        }

        // Kiểm tra xem có approved shift_change explanation không
        $hasApprovedShiftChange = false;
        if ($explanations && $explanations->isNotEmpty()) {
            $hasApprovedShiftChange = $explanations->filter(function($explanation) {
                return $explanation->final_status === 'approved' &&
                       $explanation->explanation_type === 'shift_change';
            })->isNotEmpty();
        }

        // Nếu đã có shift_change được duyệt thì không cần cảnh báo
        if ($hasApprovedShiftChange) {
            return null;
        }

        // Lấy thời gian checkin/checkout
        $checkinTime = \Carbon\Carbon::parse($timesheet->checkin);
        $checkoutTime = \Carbon\Carbon::parse($timesheet->checkout);

        // Lấy thời gian ca làm việc từ shift_start_time và shift_end_time
        if (!$timesheet->shift_start_time || !$timesheet->shift_end_time) {
            return null;
        }

        $shiftStartTime = $timesheet->shift_start_time;
        $shiftEndTime = $timesheet->shift_end_time;

        // Lấy thời gian nghỉ trưa từ shift_break_times nếu có
        $breakMinutes = 0;
        if (!empty($timesheet->shift_break_times)) {
            $breakTimes = json_decode($timesheet->shift_break_times, true);
            if (is_array($breakTimes)) {
                foreach ($breakTimes as $breakTime) {
                    if (isset($breakTime['start']) && isset($breakTime['end'])) {
                        $breakStart = \Carbon\Carbon::parse($breakTime['start']);
                        $breakEnd = \Carbon\Carbon::parse($breakTime['end']);
                        $breakMinutes += $breakStart->diffInMinutes($breakEnd);
                    }
                }
            }
        }

        // Tính thời gian làm việc thực tế và thời gian ca (tính bằng phút, trừ nghỉ trưa)
        $actualWorkMinutes = $checkinTime->diffInMinutes($checkoutTime) - $breakMinutes;
        $shiftDurationMinutes = \Carbon\Carbon::parse($shiftStartTime)->diffInMinutes(\Carbon\Carbon::parse($shiftEndTime)) - $breakMinutes;

        // Chênh lệch hơn 1 tiếng (60 phút) thì cảnh báo
        $timeDifference = abs($actualWorkMinutes - $shiftDurationMinutes);

        if ($timeDifference > 200) {
            $actualHours = round($actualWorkMinutes / 60, 1);
            $shiftHours = round($shiftDurationMinutes / 60, 1);

            $warningType = $actualWorkMinutes > $shiftDurationMinutes ? 'longer' : 'shorter';

            // Chỉ bỏ qua cảnh báo nếu:
            // 1. Làm việc thừa giờ (longer)
            // 2. Ca hiện tại đã là full-time (threshold = 1.0)
            // 3. Thời gian làm việc thực tế không vượt quá 9 tiếng (để tránh trường hợp làm việc quá nhiều)
            if ($warningType === 'longer' &&
                $timesheet->shift_default_workday_threshold == 1.0 &&
                $actualWorkMinutes <= 540) { // 9 tiếng = 540 phút
                return null; // Không cảnh báo cho ca full-time khi làm thừa giờ hợp lý
            }

            $message = $warningType === 'longer'
                ? "Làm việc {$actualHours}h nhưng ca chỉ {$shiftHours}h. Nên chọn ca dài hơn."
                : "Chỉ làm việc {$actualHours}h nhưng ca có {$shiftHours}h. Nên chọn ca ngắn hơn.";

            return $message;
        }

        return null;
    }

    /**
     * Áp dụng shift change nếu có approved explanation
     */
    private function applyShiftChangeIfApproved($timesheet, $explanations)
    {
        if (!$explanations || $explanations->isEmpty()) {
            return $timesheet;
        }

        // Tìm approved shift_change explanation
        $shiftChangeExplanation = $explanations->filter(function($explanation) {
            return $explanation->final_status === 'approved' &&
                   $explanation->explanation_type === 'shift_change' &&
                   $explanation->shift_change_id;
        })->first();

        if (!$shiftChangeExplanation) {
            return $timesheet;
        }

        // Tạo một bản copy của timesheet với shift mới
        $effectiveTimesheet = clone $timesheet;

        // Lấy thông tin shift mới
        $newShift = \App\Models\Shift::find($shiftChangeExplanation->shift_change_id);
        if (!$newShift) {
            return $timesheet; // Fallback nếu không tìm thấy shift
        }

        // Cập nhật thông tin shift trong timesheet
        $effectiveTimesheet->shift_id = $newShift->id;
        $effectiveTimesheet->shift_name = $newShift->name;
        $effectiveTimesheet->shift_start_time = $newShift->start_time;
        $effectiveTimesheet->shift_end_time = $newShift->end_time;
        $effectiveTimesheet->shift_default_workday_threshold = $newShift->default_workday_threshold;

        // Lấy shift rule history cho shift mới nếu có
        $shiftRule = $newShift->getActiveRule($timesheet->date);
        if ($shiftRule) {
            $effectiveTimesheet->shift_rule_history_id = $shiftRule->id;
            $effectiveTimesheet->shift_workday_min_1 = $shiftRule->workday_min_1;
            $effectiveTimesheet->shift_workday_min_2 = $shiftRule->workday_min_2;
            $effectiveTimesheet->shift_break_times = $shiftRule->break_times;
        }

        return $effectiveTimesheet;
    }

    /**
     * Áp dụng các giải trình đã được duyệt
     */
    private function applyExplanations($data, $explanations, $timesheet)
    {
        $explanationTexts = [];
        $totalOtHours = 0;
        $hasWorkdayAdjustment = false;

        foreach ($explanations as $explanation) {
            $explanationTexts[] = $explanation->explanation_type_text . ': ' . $explanation->explanation;

            switch ($explanation->explanation_type) {
                case 'late':
                    $data['late_explanation'] = $explanation->explanation;
                    $data['attendance_status'] = 'has_explanation';
                    $hasWorkdayAdjustment = true;
                    break;

                case 'early':
                    $data['early_explanation'] = $explanation->explanation;
                    $data['attendance_status'] = 'has_explanation';
                    $hasWorkdayAdjustment = true;
                    break;

                case 'insufficient_hours':
                case 'no_checkin':
                case 'no_checkout':
                    $data['attendance_status'] = 'has_explanation';
                    $hasWorkdayAdjustment = true;
                    break;

                case 'overtime':
                    $totalOtHours += $explanation->ot_hours ?? 0;
                    $data['attendance_status'] = 'has_overtime';
                    break;

                case 'remote_work':
                    $data['attendance_status'] = 'has_explanation';
                    $hasWorkdayAdjustment = true;
                    break;

                case 'shift_change':
                    $data['attendance_status'] = 'has_explanation';
                    $hasWorkdayAdjustment = true;
                    break;

                case 'new_employee_no_account':
                    $data['attendance_status'] = 'has_explanation';
                    $hasWorkdayAdjustment = true;
                    break;

                case 'other':
                    $data['attendance_status'] = 'has_explanation';
                    $hasWorkdayAdjustment = true;
                    break;
            }
        }

        // Cập nhật thông tin tổng hợp
        $data['explanation_summary'] = implode('; ', $explanationTexts);
        $data['overtime_hours'] = $totalOtHours;

        // Nếu có giải trình được duyệt, điều chỉnh công
        if ($hasWorkdayAdjustment) {
            $data['final_workday'] = $data['shift_default_workday_threshold'] ?? 1.0; // Giải trình được duyệt = sử dụng threshold từ shift
            $data['total_workday'] = $data['final_workday'];
        }

        return $data;
    }

    /**
     * Tính toán vi phạm với kiểm tra giải trình cho từng loại vi phạm
     */
    private function calculateViolationsWithExplanationCheck($data, $timesheet, $explanations, $isChief)
    {
        // Kiểm tra vi phạm quên checkin/checkout trước
        if (!$timesheet->checkin || !$timesheet->checkout) {
            return $this->handleMissingCheckinCheckout($data, $timesheet, $explanations, $isChief);
        }

        // Logic đã được xử lý ở determineAttendanceStatusUsingService

        if (!$timesheet->shift_start_time || !$timesheet->shift_end_time) {
            return $data;
        }

        // Luôn tính violation bất kể workday (vì đi muộn/về sớm vẫn là vi phạm)
        // Chỉ skip nếu không có checkin/checkout đã được xử lý ở trên

        $checkin = Carbon::parse($timesheet->checkin);
        $checkout = Carbon::parse($timesheet->checkout);

        // Kết hợp ngày từ timesheet với thời gian từ shift
        $timesheetDate = Carbon::parse($timesheet->date);
        $expectedCheckin = $timesheetDate->copy()->setTimeFromTimeString($timesheet->shift_start_time);
        $expectedCheckout = $timesheetDate->copy()->setTimeFromTimeString($timesheet->shift_end_time);

        // Kiểm tra đi muộn với tolerance 1 phút
        if ($checkin->gt($expectedCheckin)) {
            $lateSeconds = $checkin->diffInSeconds($expectedCheckin);
            $lateMinutes = $lateSeconds > 60 ? $lateSeconds / 60 : 0;

            // Bao gồm 'insufficient_hours' vì thiếu giờ có thể do đi muộn
            $hasLateExplanation = $this->hasSpecificExplanation($explanations, ['late', 'no_checkin', 'insufficient_hours', 'other']);

            if (!$hasLateExplanation) {
                $data['late_minutes'] = $lateMinutes;
                $data['violation_count']++;
                // Logic: Nếu đi muộn >= 120 phút thì không tính phạt tiền
                if ($lateMinutes < 120) {
                    $data['late_penalty'] = $this->calculatePenaltyAmount($data['late_minutes'], $isChief);
                }
                // Nếu >= 120 phút: có violation nhưng không có penalty tiền
            }
        }

        // Kiểm tra về sớm với tolerance 1 phút
        if ($checkout->lt($expectedCheckout)) {
            $earlySeconds = $expectedCheckout->diffInSeconds($checkout);
            $earlyMinutes = $earlySeconds > 60 ? $earlySeconds / 60 : 0;

            // Bao gồm 'insufficient_hours' vì thiếu giờ có thể do về sớm
            $hasEarlyExplanation = $this->hasSpecificExplanation($explanations, ['early', 'no_checkout', 'insufficient_hours', 'other']);

            if (!$hasEarlyExplanation) {
                $data['early_minutes'] = $earlyMinutes;
                $data['early_penalty'] = $this->calculatePenaltyAmount($data['early_minutes'], $isChief);
                $data['violation_count']++;
            }
        }

        // Tính tổng phạt
        $data['total_penalty'] = $data['late_penalty'] + $data['early_penalty'];
        $data['total_penalty_with_multiplier'] = $data['total_penalty'];

        // Cập nhật trạng thái
        if ($data['late_minutes'] > 0 || $data['early_minutes'] > 0) {
            $data['attendance_status'] = 'has_issue';
        }

        return $data;
    }

    /**
     * Xử lý vi phạm quên checkin hoặc checkout
     */
    private function handleMissingCheckinCheckout($data, $timesheet, $explanations, $isChief)
    {
        $missingCheckin = !$timesheet->checkin;
        $missingCheckout = !$timesheet->checkout;

        // Kiểm tra có giải trình cho vi phạm quên checkin/checkout không
        $hasNoCheckinExplanation = false;
        $hasNoCheckoutExplanation = false;

        if ($missingCheckin) {
            $hasNoCheckinExplanation = $this->hasSpecificExplanation($explanations, ['no_checkin', 'other']);
        }

        if ($missingCheckout) {
            $hasNoCheckoutExplanation = $this->hasSpecificExplanation($explanations, ['no_checkout', 'other']);
        }

        // Tính vi phạm và phạt (sử dụng status tiếng Anh)
        if ($missingCheckin && !$hasNoCheckinExplanation) {
            $data['violation_count']++;
            $data['attendance_status'] = 'incomplete_attendance';
            // Có thể thêm penalty cho quên checkin nếu cần
        }

        if ($missingCheckout && !$hasNoCheckoutExplanation) {
            $data['violation_count']++;
            $data['attendance_status'] = 'incomplete_attendance';
            // Có thể thêm penalty cho quên checkout nếu cần
        }

        return $data;
    }

    /**
     * Convert English status to Vietnamese for display
     */
    private function getVietnameseStatus($englishStatus)
    {
        $statusMap = [
            'normal' => 'Bình thường',
            'has_issue' => 'Có vấn đề',
            'absent' => 'Nghỉ làm',
            'has_explanation' => 'Có giải trình',
            'shift_mismatch' => 'Cảnh báo ca làm việc',
            'adjusted' => 'Đã điều chỉnh',
            'has_overtime' => 'Có tăng ca',
            'has_issue_x2' => 'Có vi phạm (x2 - từ lần 7/tháng)',
            'incomplete_attendance' => 'Quên chấm công',
        ];

        return $statusMap[$englishStatus] ?? 'Không xác định';
    }

    /**
     * Kiểm tra row có trạng thái khác "normal" không (sử dụng status tiếng Anh)
     */
    private function rowHasViolation($rowData, $timesheet)
    {
        // Kiểm tra vi phạm quên checkin/checkout
        if (!$timesheet->checkin || !$timesheet->checkout) {
            return true;
        }

        // Kiểm tra có vi phạm đi muộn/về sớm không
        if (isset($rowData['violation_count']) && $rowData['violation_count'] > 0) {
            return true;
        }

        // Kiểm tra attendance status - sử dụng status tiếng Anh trực tiếp
        if (isset($rowData['attendance_status'])) {
            // Chỉ trạng thái "normal" mới KHÔNG có viền đỏ
            if ($rowData['attendance_status'] !== 'normal') {
                return true;
            }
        }

        return false;
    }

    /**
     * Tính toán vi phạm khi không có giải trình (method cũ - giữ lại để tương thích)
     */
    private function calculateViolations($data, $timesheet, $isChief)
    {
        if (!$timesheet->checkin || !$timesheet->checkout || !$timesheet->shift_start_time || !$timesheet->shift_end_time) {
            return $data;
        }

        $checkin = Carbon::parse($timesheet->checkin);
        $checkout = Carbon::parse($timesheet->checkout);

        // Kết hợp ngày từ timesheet với thời gian từ shift
        $timesheetDate = Carbon::parse($timesheet->date);
        $expectedCheckin = $timesheetDate->copy()->setTimeFromTimeString($timesheet->shift_start_time);
        $expectedCheckout = $timesheetDate->copy()->setTimeFromTimeString($timesheet->shift_end_time);

        // Tính đi muộn với tolerance 1 phút
        if ($checkin->gt($expectedCheckin)) {
            $lateSeconds = $checkin->diffInSeconds($expectedCheckin);
            $lateMinutes = $lateSeconds > 60 ? $lateSeconds / 60 : 0;

            $data['late_minutes'] = $lateMinutes;
            $data['violation_count']++;
            // Logic: Nếu đi muộn >= 120 phút thì không tính phạt tiền
            if ($lateMinutes < 120) {
                $data['late_penalty'] = $this->calculatePenaltyAmount($data['late_minutes'], $isChief);
            }
            // Nếu >= 120 phút: có violation nhưng không có penalty tiền
        }

        // Tính về sớm với tolerance 1 phút
        if ($checkout->lt($expectedCheckout)) {
            $earlySeconds = $expectedCheckout->diffInSeconds($checkout);
            $earlyMinutes = $earlySeconds > 60 ? $earlySeconds / 60 : 0;

            $data['early_minutes'] = $earlyMinutes;
            $data['early_penalty'] = $this->calculatePenaltyAmount($data['early_minutes'], $isChief);
            $data['violation_count']++;
        }



        // Tính tổng phạt
        $data['total_penalty'] = $data['late_penalty'] + $data['early_penalty'];
        $data['total_penalty_with_multiplier'] = $data['total_penalty'];

        // Cập nhật trạng thái
        if ($data['late_minutes'] > 0 || $data['early_minutes'] > 0) {
            $data['attendance_status'] = 'has_issue';
        }

        return $data;
    }

    // Removed old calculatePenalty method - using calculatePenaltyAmount instead

    /**
     * Áp dụng quy tắc x2 phạt cho vi phạm từ lần thứ 7 trở đi trong tháng
     * Logic: Vi phạm 1-6 phạt bình thường, vi phạm 7+ phạt x2
     *
     * Giữ nguyên late_penalty và early_penalty (số tiền gốc)
     * Tạo total_penalty_with_multiplier (số tiền đã x2)
     */
    private function applyMonthlyViolationMultiplier($data, $timesheet)
    {
        // Chỉ áp dụng nếu có vi phạm trong ngày hiện tại
        if ($data['violation_count'] == 0) {
            $data['total_penalty_with_multiplier'] = $data['total_penalty'];
            return $data;
        }

        // Đếm vi phạm trước ngày hiện tại
        $violationCountBeforeThisDate = $this->getViolationCountBeforeDate($timesheet->user_id, $timesheet->date);

        $originalLatePenalty = $data['late_penalty'];
        $originalEarlyPenalty = $data['early_penalty'];
        $multipliedLatePenalty = 0;
        $multipliedEarlyPenalty = 0;

        // Đếm vi phạm tích lũy giống TimesheetSummaryExport
        $currentViolationNumber = $violationCountBeforeThisDate;

        // Xử lý penalty cho vi phạm đi muộn
        if ($data['late_minutes'] > 0) {
            $currentViolationNumber++; // Đây là vi phạm thứ N
            if ($currentViolationNumber >= 7) {
                $multipliedLatePenalty = $originalLatePenalty * 2;
            } else {
                $multipliedLatePenalty = $originalLatePenalty;
            }
        }

        // Xử lý penalty cho vi phạm về sớm
        if ($data['early_minutes'] > 0) {
            $currentViolationNumber++; // Đây là vi phạm thứ N+1
            if ($currentViolationNumber >= 7) {
                $multipliedEarlyPenalty = $originalEarlyPenalty * 2;
            } else {
                $multipliedEarlyPenalty = $originalEarlyPenalty;
            }
        }

        // Giữ nguyên late_penalty và early_penalty (số tiền gốc)
        // Chỉ cập nhật total_penalty_with_multiplier (số tiền đã x2)
        $data['total_penalty_with_multiplier'] = $multipliedLatePenalty + $multipliedEarlyPenalty;

        // Cập nhật trạng thái nếu có áp dụng x2
        if (($multipliedLatePenalty > $originalLatePenalty) || ($multipliedEarlyPenalty > $originalEarlyPenalty)) {
            if ($data['attendance_status'] === 'has_issue') {
                $data['attendance_status'] = 'has_issue_x2';
            }
        }

        return $data;
    }

    /**
     * Đếm tổng số lần vi phạm trong tháng (bao gồm cả vi phạm không phạt tiền)
     * Sử dụng repository để có đầy đủ shift_rule_histories data
     */
    private function getMonthlyViolationCount($userId, $date)
    {
        $startOfMonth = Carbon::parse($date)->startOfMonth();
        $endOfMonth = Carbon::parse($date)->endOfMonth();

        // Sử dụng repository để có đầy đủ shift_rule_histories data
        $timesheets = $this->timesheetRepository->queryToExport([
            'user_ids' => [$userId],
            'from_date' => $startOfMonth->format('Y-m-d'),
            'to_date' => $endOfMonth->format('Y-m-d'),
        ])->whereNotNull('checkin')
          ->whereNotNull('checkout')
          ->get();

        $totalViolationCount = 0;

        foreach ($timesheets as $timesheet) {
            // Kiểm tra có giải trình được duyệt không
            $hasApprovedExplanation = AttendanceExplanation::where('user_id', $userId)
                ->whereDate('date', $timesheet->date)
                ->where('final_status', 'approved')
                ->exists();

            // Nếu có giải trình được duyệt thì bỏ qua
            if ($hasApprovedExplanation) {
                continue;
            }

            // Đếm tất cả vi phạm (cả phạt tiền và không phạt tiền)
            $violationCount = $this->countAllViolations($timesheet);
            $totalViolationCount += $violationCount;
        }

        return $totalViolationCount;
    }

    /**
     * Đếm số vi phạm trước ngày hiện tại trong tháng
     * Dùng để xác định vị trí vi phạm (1-6 hay 7+) cho logic x2
     */
    private function getViolationCountBeforeDate($userId, $currentDate)
    {
        $startOfMonth = Carbon::parse($currentDate)->startOfMonth();
        $beforeCurrentDate = Carbon::parse($currentDate)->subDay();

        // Sử dụng repository để có đầy đủ shift_rule_histories data
        $timesheets = $this->timesheetRepository->queryToExport([
            'user_ids' => [$userId],
            'from_date' => $startOfMonth->format('Y-m-d'),
            'to_date' => $beforeCurrentDate->format('Y-m-d'),
        ])->whereNotNull('checkin')
          ->whereNotNull('checkout')
          ->get();

        $totalViolationCount = 0;

        foreach ($timesheets as $timesheet) {
            // Kiểm tra có giải trình được duyệt không
            $hasApprovedExplanation = AttendanceExplanation::where('user_id', $userId)
                ->whereDate('date', $timesheet->date)
                ->where('final_status', 'approved')
                ->exists();

            // Nếu có giải trình được duyệt thì bỏ qua
            if ($hasApprovedExplanation) {
                continue;
            }

            // Đếm tất cả vi phạm (cả phạt tiền và không phạt tiền)
            $violationCount = $this->countAllViolations($timesheet);
            $totalViolationCount += $violationCount;
        }

        return $totalViolationCount;
    }

    /**
     * Đếm tất cả vi phạm trong một ngày (cả phạt tiền và không phạt tiền)
     * Sử dụng shift_rule_histories data thay vì shifts table
     */
    private function countAllViolations($timesheet)
    {
        if (!$timesheet->checkin || !$timesheet->checkout) {
            return 0;
        }

        $checkin = \Carbon\Carbon::parse($timesheet->checkin);
        $checkout = \Carbon\Carbon::parse($timesheet->checkout);
        $dateString = $checkin->format('Y-m-d');

        // Sử dụng shift_rule_histories data thay vì shifts table
        // Lấy thời gian từ shift_start_time và shift_end_time (đã được join từ shift_rule_histories)
        $shiftStartTime = $timesheet->shift_start_time ?? ($timesheet->shift ? $timesheet->shift->start_time : null);
        $shiftEndTime = $timesheet->shift_end_time ?? ($timesheet->shift ? $timesheet->shift->end_time : null);

        if (!$shiftStartTime || !$shiftEndTime) {
            return 0;
        }

        $expectedCheckin = \Carbon\Carbon::parse($dateString . ' ' . $shiftStartTime);
        $expectedCheckout = \Carbon\Carbon::parse($dateString . ' ' . $shiftEndTime);

        $violationCount = 0;

        // Đếm vi phạm đi muộn (tất cả mức độ)
        if ($checkin->gt($expectedCheckin)) {
            $violationCount++;
        }

        // Đếm vi phạm về sớm (tất cả mức độ)
        if ($checkout->lt($expectedCheckout)) {
            $violationCount++;
        }

        return $violationCount;
    }

    /**
     * Kiểm tra vi phạm có bị phạt tiền không (áp dụng logic 120 phút)
     */
    private function hasMonetaryPenaltyViolation($timesheet)
    {
        if (!$timesheet->shift || !$timesheet->checkin || !$timesheet->checkout) {
            return false;
        }

        $checkin = Carbon::parse($timesheet->checkin);
        $checkout = Carbon::parse($timesheet->checkout);
        $timesheetDate = Carbon::parse($timesheet->date);

        // Ưu tiên sử dụng shift_rule_histories thay vì shift
        $shift = $timesheet->shift;
        $shiftRule = $timesheet->shiftRuleHistory;

        $scheduleStart = $shiftRule ? $shiftRule->start_time : $shift->start_time;
        $scheduleEnd = $shiftRule ? $shiftRule->end_time : $shift->end_time;

        $expectedCheckin = $timesheetDate->copy()->setTimeFromTimeString($scheduleStart);
        $expectedCheckout = $timesheetDate->copy()->setTimeFromTimeString($scheduleEnd);

        $hasMonetaryPenalty = false;

        // Kiểm tra đi muộn với tolerance 1 phút
        if ($checkin->gt($expectedCheckin)) {
            $lateSeconds = $checkin->diffInSeconds($expectedCheckin);
            $lateMinutes = $lateSeconds > 60 ? $lateSeconds / 60 : 0;

            // Logic mới: Chỉ tính phạt nếu đi muộn < 120 phút
            if ($lateMinutes < 120) {
                $hasMonetaryPenalty = true;
            }
        }

        // Kiểm tra về sớm với tolerance 1 phút (áp dụng logic 120 phút cho về sớm)
        if ($checkout->lt($expectedCheckout)) {
            $earlySeconds = $expectedCheckout->diffInSeconds($checkout);
            $earlyMinutes = $earlySeconds > 60 ? $earlySeconds / 60 : 0;

            // Kiểm tra có giải trình được duyệt không
            $hasEarlyExplanation = $this->hasApprovedExplanation($timesheet->user_id, $timesheet->date, ['early', 'no_checkout', 'insufficient_hours', 'other']);
            if (!$hasEarlyExplanation) {
                $hasMonetaryPenalty = true;
            }
        }

        return $hasMonetaryPenalty;
    }

    /**
     * Parse break times from database (handle double-encoded JSON)
     */
    private function parseBreakTimes($breakTimesData)
    {
        if (empty($breakTimesData)) {
            return null;
        }

        $breakTimes = null;
        if (is_string($breakTimesData)) {
            // First decode
            $firstDecode = json_decode($breakTimesData, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                // Check if result is still a string (double-encoded)
                if (is_string($firstDecode)) {
                    // Second decode
                    $breakTimes = json_decode($firstDecode, true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        return null; // Invalid JSON
                    }
                } else {
                    $breakTimes = $firstDecode;
                }
            } else {
                return null; // Invalid JSON
            }
        } else {
            $breakTimes = $breakTimesData;
        }

        return is_array($breakTimes) ? $breakTimes : null;
    }

    /**
     * Check if explanations contain specific types
     */
    private function hasExplanationForType($explanations, $types)
    {
        if (!$explanations || $explanations->isEmpty()) {
            return false;
        }

        return $explanations->where('final_status', 'approved')
            ->whereIn('explanation_type', $types)
            ->isNotEmpty();
    }

    /**
     * Calculate penalty amount based on minutes and position (bảng phạt chính thức)
     */
    private function calculatePenaltyAmount($minutes, $isChief)
    {
        if ($minutes < 1) {
            return 0;
        }

        // Bảng phạt cho Nhân viên
        if (!$isChief) {
            if ($minutes >= 1 && $minutes <= 10) return 50000;
            if ($minutes >= 11 && $minutes <= 30) return 100000;
            if ($minutes >= 31 && $minutes <= 60) return 120000;
            if ($minutes >= 61 && $minutes <= 120) return 200000;
            if ($minutes > 120) return 0; // Không phạt tiền cho >120 phút
        }
        // Bảng phạt cho Trưởng nhóm
        else {
            if ($minutes >= 1 && $minutes <= 10) return 50000;
            if ($minutes >= 11 && $minutes <= 30) return 100000;
            if ($minutes >= 31 && $minutes <= 60) return 150000;
            if ($minutes >= 61 && $minutes <= 120) return 250000;
            if ($minutes > 120) return 0; // Không phạt tiền cho >120 phút
        }

        return 0;
    }

    /**
     * Kiểm tra timesheet có vi phạm không
     */
    private function hasViolation($timesheet)
    {
        if (!$timesheet->shift || !$timesheet->checkin || !$timesheet->checkout) {
            return false;
        }

        $checkin = Carbon::parse($timesheet->checkin);
        $checkout = Carbon::parse($timesheet->checkout);
        $timesheetDate = Carbon::parse($timesheet->date);

        // Ưu tiên sử dụng shift_rule_histories thay vì shift
        $shift = $timesheet->shift;
        $shiftRule = $timesheet->shiftRuleHistory;

        $scheduleStart = $shiftRule ? $shiftRule->start_time : $shift->start_time;
        $scheduleEnd = $shiftRule ? $shiftRule->end_time : $shift->end_time;

        $expectedCheckin = $timesheetDate->copy()->setTimeFromTimeString($scheduleStart);
        $expectedCheckout = $timesheetDate->copy()->setTimeFromTimeString($scheduleEnd);

        // Có vi phạm nếu đi muộn hoặc về sớm
        return $checkin->gt($expectedCheckin) || $checkout->lt($expectedCheckout);
    }

    /**
     * Lấy thông tin ca làm việc từ shift_rule_histories hoặc fallback về shifts
     */
    private function populateShiftRuleData($data, $timesheet)
    {
        // Ưu tiên lấy từ shift_rule_histories nếu có
        if (!empty($timesheet->shift_break_times)) {
            // Có dữ liệu từ shift_rule_histories
            $data['rule_start_time'] = $this->formatTime($timesheet->shift_start_time);
            $data['rule_end_time'] = $this->formatTime($timesheet->shift_end_time);
            $data['rule_break_times'] = $this->formatBreakTimes($timesheet->shift_break_times);
        } else {
            // Fallback về shifts (dữ liệu cũ)
            $data['rule_start_time'] = $this->formatTime($timesheet->shift_start_time);
            $data['rule_end_time'] = $this->formatTime($timesheet->shift_end_time);
            $data['rule_break_times'] = 'Không có dữ liệu';
        }

        return $data;
    }

    /**
     * Format thời gian để hiển thị
     */
    private function formatTime($time)
    {
        if (!$time) return '';

        try {
            return Carbon::parse($time)->format('H:i');
        } catch (\Exception $e) {
            return $time;
        }
    }

    /**
     * Format break times từ JSON để hiển thị
     */
    private function formatBreakTimes($breakTimesJson)
    {
        if (!$breakTimesJson) return '';

        try {
            $breakTimes = json_decode($breakTimesJson, true);
            if (!is_array($breakTimes)) return '';

            $formatted = [];
            foreach ($breakTimes as $breakTime) {
                if (isset($breakTime['start']) && isset($breakTime['end'])) {
                    $formatted[] = $breakTime['start'] . '-' . $breakTime['end'];
                }
            }

            return implode(', ', $formatted);
        } catch (\Exception $e) {
            return $breakTimesJson;
        }
    }

    /**
     * Áp dụng giải trình khi không có timesheet
     */
    private function applyExplanationsWithoutTimesheet($data, $explanations)
    {
        $explanationSummaries = [];
        $totalOvertimeHours = 0;

        foreach ($explanations as $explanation) {
            switch ($explanation->explanation_type) {
                case 'overtime':
                    $totalOvertimeHours += floatval($explanation->ot_hours ?? 0);
                    // Giải trình OT không có timesheet = chỉ có giờ OT, không tính công
                    $data['final_workday'] = 0;
                    $data['total_workday'] = 0;
                    $data['attendance_status'] = 'overtime_only';
                    $explanationSummaries[] = "Làm thêm giờ (OT): " . ($explanation->explanation ?? '');
                    break;
                case 'remote_work':
                    // Lấy workday từ remote_shift_id
                    $workday = $this->getWorkdayFromShift($explanation->remote_shift_id);
                    $data['final_workday'] = $workday;
                    $data['total_workday'] = $workday;
                    $data['attendance_status'] = 'has_explanation';
                    $explanationSummaries[] = "Làm việc từ xa: " . ($explanation->explanation ?? '');
                    break;
                case 'shift_change':
                    // Giải trình đổi ca không có timesheet = không thể đổi ca
                    $data['final_workday'] = 0;
                    $data['total_workday'] = 0;
                    $data['attendance_status'] = 'shift_change_no_timesheet';
                    $explanationSummaries[] = "Giải trình đổi ca (không có chấm công): " . ($explanation->explanation ?? '');
                    break;
                default:
                    $data['final_workday'] = 1;
                    $data['total_workday'] = 1;
                    $data['attendance_status'] = 'has_explanation';
                    $explanationSummaries[] = $explanation->explanation ?? '';
                    break;
            }
        }

        $data['overtime_hours'] = $totalOvertimeHours;
        $data['explanation_summary'] = implode('; ', $explanationSummaries);

        return $data;
    }

    /**
     * Lấy workday từ shift_id
     */
    private function getWorkdayFromShift($shiftId)
    {
        if (!$shiftId) {
            return 1; // Default nếu không có shift_id
        }

        $shift = \App\Models\Shift::find($shiftId);
        if (!$shift) {
            return 1; // Default nếu không tìm thấy shift
        }

        return floatval($shift->default_workday_threshold ?? 1);
    }


}
