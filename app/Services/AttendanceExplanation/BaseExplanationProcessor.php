<?php

namespace App\Services\AttendanceExplanation;

use App\Services\AttendanceExplanation\Contracts\ExplanationProcessorInterface;
use Illuminate\Support\Collection;
use Carbon\Carbon;

abstract class BaseExplanationProcessor implements ExplanationProcessorInterface
{
    /**
     * Tạo base attendance data structure
     *
     * @return array
     */
    protected function createBaseAttendanceData(): array
    {
        return [
            'workday' => 0,
            'overtime_hours' => 0,
            'late_arrival_minutes' => 0,
            'early_departure_minutes' => 0,
            'late_arrival_penalty' => 0,
            'early_departure_penalty' => 0,
            'total_penalty' => 0,
            'attendance_status' => '',
            'explanation' => '',
            'shift_mismatch_warning' => false,
        ];
    }

    /**
     * Lấy workday từ shift ID
     *
     * @param int|null $shiftId
     * @return float
     */
    protected function getWorkdayFromShift(?int $shiftId): float
    {
        if (!$shiftId) {
            return 1.0; // Default workday
        }

        $shift = \App\Models\Shift::find($shiftId);
        return $shift ? ($shift->default_workday_threshold ?? 1.0) : 1.0;
    }

    /**
     * Parse break times từ JSON string
     *
     * @param string|null $breakTimesJson
     * @return array
     */
    protected function parseBreakTimes(?string $breakTimesJson): array
    {
        if (empty($breakTimesJson)) {
            return [];
        }

        $breakTimes = json_decode($breakTimesJson, true);
        return is_array($breakTimes) ? $breakTimes : [];
    }

    /**
     * Tính toán penalty amount
     *
     * @param float $minutes
     * @param bool $isChief
     * @return float
     */
    protected function calculatePenaltyAmount(float $minutes, bool $isChief = false): float
    {
        // Logic tính penalty - có thể customize trong từng processor
        $baseAmount = 5000; // Base penalty amount
        $multiplier = $isChief ? 0.5 : 1.0; // Chief có penalty thấp hơn
        
        return $minutes * $baseAmount * $multiplier;
    }

    /**
     * Kiểm tra có explanation cho các loại vi phạm cụ thể
     *
     * @param Collection $explanations
     * @param array $explanationTypes
     * @return bool
     */
    protected function hasExplanationForType(Collection $explanations, array $explanationTypes): bool
    {
        if ($explanations->isEmpty()) {
            return false;
        }

        return $explanations->whereIn('explanation_type', $explanationTypes)->isNotEmpty();
    }

    /**
     * Lấy explanation text summary
     *
     * @param Collection $explanations
     * @return string
     */
    public function getExplanationSummary(Collection $explanations): string
    {
        if ($explanations->isEmpty()) {
            return '';
        }

        return $explanations->pluck('explanation')->join('; ');
    }

    /**
     * Mặc định không ảnh hưởng đến penalty
     *
     * @return bool
     */
    public function affectsPenalty(): bool
    {
        return false;
    }
}
