<?php

namespace App\Services\AttendanceExplanation;

use App\Models\Timesheet;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class AttendanceDataProcessor
{
    protected TimesheetConverter $converter;
    protected AttendanceStatusCalculator $statusCalculator;
    protected WorkdayCalculator $workdayCalculator;

    public function __construct(
        TimesheetConverter $converter,
        AttendanceStatusCalculator $statusCalculator,
        WorkdayCalculator $workdayCalculator
    ) {
        $this->converter = $converter;
        $this->statusCalculator = $statusCalculator;
        $this->workdayCalculator = $workdayCalculator;
    }

    /**
     * Tính toán trạng thái chấm công cho nhiều timesheets trong một ngày
     */
    public function calculateMultipleTimesheetsStatus(Collection $timesheets, Collection $explanations = null): array
    {
        $explanations = $explanations ?? collect();

        // Lấy timesheet đầu tiên để làm base data
        $primaryTimesheet = $timesheets->first();

        // <PERSON><PERSON><PERSON> tra xem có giải trình thay đổi ca được approved không
        $approvedShiftChange = $this->getApprovedShiftChangeExplanation($explanations);
        $effectiveShift = $approvedShiftChange ? $approvedShiftChange->shiftChange : $primaryTimesheet->shift;

        // Debug logging for user 2845 date 2025-06-26
        if ($primaryTimesheet->user_id == 2845 && $primaryTimesheet->date->format('Y-m-d') == '2025-06-26') {
            \Log::info('DEBUG User 2845 - 2025-06-26 - Multiple Timesheets', [
                'total_explanations' => $explanations->count(),
                'explanations_data' => $explanations->map(function($exp) {
                    return [
                        'id' => $exp->id,
                        'type' => $exp->explanation_type,
                        'final_status' => $exp->final_status,
                        'remote_shift_id' => $exp->remote_shift_id
                    ];
                })->toArray(),
                'approved_shift_change_id' => $approvedShiftChange ? $approvedShiftChange->id : null,
                'effective_shift_name' => $effectiveShift ? $effectiveShift->name : null
            ]);
        }

        // Debug logging for shift change
        if ($explanations->isNotEmpty()) {
            \Log::info('AttendanceDataProcessor - Checking explanations', [
                'date' => $primaryTimesheet->date->format('Y-m-d'),
                'total_explanations' => $explanations->count(),
                'approved_explanations' => $explanations->where('final_status', 'approved')->count(),
                'shift_change_explanations' => $explanations->where('explanation_type', 'shift_change')->count(),
                'approved_shift_change' => $approvedShiftChange ? $approvedShiftChange->id : 'none',
                'effective_shift_name' => $effectiveShift ? $effectiveShift->name : 'none'
            ]);
        }

        // Convert timesheet nếu cần
        if ($this->converter->needsConversion($primaryTimesheet)) {
            $primaryTimesheet = $this->converter->convertFromStdClass($primaryTimesheet);
        }

        // Tính toán dữ liệu gốc từ timesheet chính
        $baseData = $this->calculateOriginalAttendanceStatus($primaryTimesheet);

        // 1. Tính workday từ tất cả timesheets
        $totalWorkday = $this->workdayCalculator->getFinalWorkdayForMultiple($timesheets, $explanations);

        // 2. Từ checkin đầu tiên đến checkout cuối cùng
        $overallRealHours = $this->workdayCalculator->calculateOverallRealHours($timesheets);

        // Sử dụng overall real hours (từ checkin đầu đến checkout cuối)
        $totalRealHours = $overallRealHours;

        // 3. Tính tổng work_hours từ tất cả timesheets
        $totalWorkHours = $this->calculateTotalWorkHours($timesheets);

        // Áp dụng điều chỉnh từ giải trình đã duyệt
        $finalWorkday = $this->workdayCalculator->getFinalWorkdayForMultiple($timesheets, $explanations);
        $finalOtHours = $this->workdayCalculator->getFinalOtHours($explanations);

        // Tính checkin đầu tiên và checkout cuối cùng thông minh
        $smartCheckin = $this->getEarliestCheckin($timesheets);
        $smartCheckout = $this->getLatestValidCheckout($timesheets);

        // Tính toán late/early minutes dựa trên smart checkin/checkout
        // Sử dụng ca hiệu lực (ca mới nếu có giải trình thay đổi ca được approved)
        [$smartLateMinutes, $smartEarlyMinutes] = $this->statusCalculator->calculateSmartLateEarlyMinutes(
            $smartCheckin, $smartCheckout, $primaryTimesheet, $effectiveShift
        );

        // Tạo checkin_checkout_records cho multiple timesheets
        $checkinCheckoutRecords = $this->createCheckinCheckoutRecords($timesheets);

        $result = array_merge($baseData, [
            'checkin_checkout_records' => $checkinCheckoutRecords,
            'total_records' => $timesheets->count(),
            'original_workday' => $totalWorkday,
            'workday' => $finalWorkday,
            'final_workday' => $finalWorkday,  // Alias cho tương thích với export
            'work_hours' => round($totalWorkHours, 2), // Override với tổng work_hours từ tất cả timesheets
            'real_hours' => round($totalRealHours, 2),
            'real_hours_formatted' => $this->workdayCalculator->formatHoursToHoursMinutes($totalRealHours),
            'is_full_workday' => $this->workdayCalculator->checkIsFullWorkdayForMultiple($timesheets, $effectiveShift),
            'approved_ot_hours' => $finalOtHours,
            'is_adjusted' => $this->calculateIsAdjusted($explanations, $finalWorkday, $totalWorkday, $finalOtHours, $approvedShiftChange),
            'adjustment_reason' => $this->getAdjustmentReason($explanations),
            // Override với smart checkin/checkout
            'checkin' => $smartCheckin,
            'checkout' => $smartCheckout,
            'late_minutes' => $smartLateMinutes,
            'early_minutes' => $smartEarlyMinutes,
            // Thông tin ca hiệu lực (ca mới nếu có giải trình thay đổi ca được approved)
            'effective_shift' => $effectiveShift,
            'shift_name' => $effectiveShift ? $effectiveShift->name : $baseData['shift_name'],
            'schedule_start' => $effectiveShift ? $effectiveShift->start_time : $baseData['schedule_start'],
            'schedule_end' => $effectiveShift ? $effectiveShift->end_time : $baseData['schedule_end'],
        ]);

        // Debug logging for user 2845 date 2025-06-26
        if ($primaryTimesheet->user_id == 2845 && $primaryTimesheet->date->format('Y-m-d') == '2025-06-26') {
            $isAdjustedCalculated = $finalWorkday !== $totalWorkday || $finalOtHours > 0 || $approvedShiftChange;
            \Log::info('DEBUG User 2845 - 2025-06-26 - Workday Calculation', [
                'total_workday' => $totalWorkday,
                'final_workday' => $finalWorkday,
                'workday_changed' => $finalWorkday !== $totalWorkday,
                'final_ot_hours' => $finalOtHours,
                'ot_hours_greater_than_zero' => $finalOtHours > 0,
                'has_approved_shift_change' => $approvedShiftChange ? true : false,
                'is_adjusted_calculated' => $isAdjustedCalculated,
                'is_adjusted_components' => [
                    'workday_changed' => $finalWorkday !== $totalWorkday,
                    'has_ot' => $finalOtHours > 0,
                    'has_shift_change' => $approvedShiftChange ? true : false
                ]
            ]);
        }

        // Tính toán lại status dựa trên smart late/early minutes và ca hiệu lực
        $smartStatus = $this->statusCalculator->determineAttendanceStatus(
            $finalWorkday, $smartLateMinutes, $smartEarlyMinutes, $primaryTimesheet, $effectiveShift
        );
        $result['status'] = $smartStatus;
        $result['status_text'] = $this->statusCalculator->getStatusText($smartStatus);
        $result['status_color'] = $this->statusCalculator->getStatusColor($smartStatus);

        // Nếu có giải trình, cập nhật status dựa trên giải trình đã được duyệt
        if ($explanations->isNotEmpty()) {
            $finalStatus = $this->determineFinalStatusWithExplanations($result, $explanations);
            $result['status'] = $finalStatus['status'];
            $result['status_color'] = $finalStatus['color'];
            $result['status_text'] = $finalStatus['text'];
        }

        return $result;
    }

    /**
     * Tính toán trạng thái chấm công cho một timesheet (với giải trình)
     */
    public function calculateAttendanceStatus(Timesheet $timesheet, Collection $explanations = null): array
    {
        $explanations = $explanations ?? collect();

        // Kiểm tra xem có giải trình được approved không
        $hasApprovedExplanations = $explanations->where('final_status', 'approved')->isNotEmpty();

        // Tính toán dữ liệu gốc
        $originalData = $this->calculateOriginalAttendanceStatus($timesheet);

        // Áp dụng điều chỉnh từ giải trình đã duyệt
        $finalWorkday = $this->workdayCalculator->getFinalWorkday($timesheet, $explanations);
        $finalOtHours = $this->workdayCalculator->getFinalOtHours($explanations);

        // Tạo checkin_checkout_records cho single timesheet
        $checkinCheckoutRecord = [
            'id' => $timesheet->id,
            'checkin' => $originalData['checkin'],
            'checkout' => $originalData['checkout'],
            'shift_name' => $originalData['shift_name'],
            'checkin_company_address' => $this->formatCompanyAddress($timesheet->companyAddress),
            'checkout_company_address' => $this->formatCompanyAddress($timesheet->checkoutCompanyAddress),
        ];

        // Kiểm tra xem có giải trình thay đổi ca được approved không
        $approvedShiftChange = $this->getApprovedShiftChangeExplanation($explanations);
        $effectiveShift = $approvedShiftChange ? $approvedShiftChange->shiftChange : $timesheet->shift;

        $result = array_merge($originalData, [
            'checkin_checkout_records' => [$checkinCheckoutRecord],
            'total_records' => 1,
            'original_workday' => $originalData['workday'],
            'workday' => $finalWorkday,
            'final_workday' => $finalWorkday,  // Alias cho tương thích với export
            'real_hours' => round($originalData['real_hours'], 2),
            'real_hours_formatted' => $this->workdayCalculator->formatHoursToHoursMinutes($originalData['real_hours']),
            'is_full_workday' => $this->workdayCalculator->checkIsFullWorkday($finalWorkday, $effectiveShift),
            'approved_ot_hours' => $finalOtHours,
            'is_adjusted' => $this->calculateIsAdjusted($explanations, $finalWorkday, $originalData['workday'], $finalOtHours),
            'adjustment_reason' => $this->getAdjustmentReason($explanations),
        ]);

        // Nếu có giải trình, cập nhật status dựa trên giải trình đã được duyệt
        if ($explanations->isNotEmpty()) {
            $finalStatus = $this->determineFinalStatusWithExplanations($result, $explanations);
            $result['status'] = $finalStatus['status'];
            $result['status_color'] = $finalStatus['color'];
            $result['status_text'] = $finalStatus['text'];
        }

        return $result;
    }

    /**
     * Tính toán trạng thái chấm công gốc (không có điều chỉnh)
     */
    public function calculateOriginalAttendanceStatus($timesheet): array
    {
        // Đảm bảo $timesheet là Timesheet model
        if ($this->converter->needsConversion($timesheet)) {
            $timesheet = $this->converter->convertFromStdClass($timesheet);
        }
        
        $shift = $timesheet->shift;
        $shiftRule = $timesheet->shiftRuleHistory;

        if (!$shift) {
            return [
                'date' => $timesheet->date,
                'checkin' => $timesheet->checkin,
                'checkout' => $timesheet->checkout,
                'shift_name' => null,
                'schedule_start' => null,
                'schedule_end' => null,
                'workday' => 0,
                'work_hours' => 0,
                'real_hours' => 0,
                'late_minutes' => 0,
                'early_minutes' => 0,
                'status' => 'no_data',
                'status_text' => 'Chưa chấm công',
                'status_color' => 'default',
            ];
        }

        // Lấy thời gian ca làm việc từ shift rule hoặc shift
        $scheduleStart = $shiftRule ? $shiftRule->start_time : $shift->start_time;
        $scheduleEnd = $shiftRule ? $shiftRule->end_time : $shift->end_time;

        // Tính toán các metrics
        $workday = $timesheet->calcWorkday();
        $workHours = $timesheet->calcWorkDayHours();
        $realHours = $timesheet->calcWorkDayRealHours();

        // Tính late/early minutes
        $lateMinutes = $this->calculateLateMinutes($timesheet, $scheduleStart);
        $earlyMinutes = $this->calculateEarlyMinutes($timesheet, $scheduleEnd);

        // Xác định trạng thái
        $status = $this->statusCalculator->determineAttendanceStatus($workday, $lateMinutes, $earlyMinutes, $timesheet);

        return [
            'date' => $timesheet->date,
            'checkin' => $timesheet->checkin,
            'checkout' => $timesheet->checkout,
            'shift_name' => $shift->name,
            'schedule_start' => $scheduleStart,
            'schedule_end' => $scheduleEnd,
            'workday' => $workday,
            'work_hours' => $workHours,
            'real_hours' => $realHours,
            'late_minutes' => $lateMinutes,
            'early_minutes' => $earlyMinutes,
            'status' => $status,
            'status_text' => $this->statusCalculator->getStatusText($status),
            'status_color' => $this->statusCalculator->getStatusColor($status),
        ];
    }

    /**
     * Lấy checkin sớm nhất từ tất cả timesheets
     */
    protected function getEarliestCheckin(Collection $timesheets)
    {
        return $timesheets->filter(function($timesheet) {
            $checkin = is_object($timesheet) && isset($timesheet->checkin) ? $timesheet->checkin : null;
            return $checkin !== null;
        })->min('checkin');
    }

    /**
     * Lấy checkout cuối cùng hợp lệ
     * Logic:
     * 1. Nếu bản ghi cuối có checkout → Lấy checkout của bản ghi cuối
     * 2. Nếu bản ghi cuối không có checkout → Lấy giờ checkin làm checkout
     * 3. Fallback: Lấy checkout muộn nhất từ các bản ghi khác
     */
    protected function getLatestValidCheckout(Collection $timesheets)
    {
        // Sắp xếp theo thời gian checkin để lấy bản ghi cuối cùng
        $sortedTimesheets = $timesheets->sortBy('checkin');
        $lastTimesheet = $sortedTimesheets->last();

        if ($lastTimesheet) {
            // Case 1: Bản ghi cuối có checkout → Lấy checkout của bản ghi cuối
            if (isset($lastTimesheet->checkout) && $lastTimesheet->checkout) {
                return $lastTimesheet->checkout;
            }

            // Case 2: Bản ghi cuối không có checkout → Lấy giờ checkin làm checkout
            if (isset($lastTimesheet->checkin) && $lastTimesheet->checkin) {
                return $lastTimesheet->checkin;
            }
        }

        // Case 3: Fallback - Lấy checkout muộn nhất từ các bản ghi khác
        $timesheetsWithCheckout = $timesheets->filter(function($timesheet) {
            $checkout = is_object($timesheet) && isset($timesheet->checkout) ? $timesheet->checkout : null;
            return $checkout !== null;
        });

        if ($timesheetsWithCheckout->isNotEmpty()) {
            return $timesheetsWithCheckout->max('checkout');
        }

        // Nếu không có checkout nào, lấy checkin muộn nhất
        return $timesheets->max('checkin');
    }

    /**
     * Tính tổng work_hours từ tất cả timesheets
     */
    protected function calculateTotalWorkHours(Collection $timesheets): float
    {
        return $timesheets->sum(function($timesheet) {
            // Đảm bảo $timesheet là Timesheet model
            if ($this->converter->needsConversion($timesheet)) {
                $timesheet = $this->converter->convertFromStdClass($timesheet);
            }
            return $timesheet->calcWorkDayHours();
        });
    }

    /**
     * Tạo checkin_checkout_records cho multiple timesheets
     */
    protected function createCheckinCheckoutRecords(Collection $timesheets): array
    {
        return $timesheets->map(function($timesheet) {
            // Convert nếu cần
            if ($this->converter->needsConversion($timesheet)) {
                $convertedTimesheet = $this->converter->convertFromStdClass($timesheet);
            } else {
                $convertedTimesheet = $timesheet;
            }

            return [
                'id' => $timesheet->id ?? null,
                'checkin' => $timesheet->checkin ?? null,
                'checkout' => $timesheet->checkout ?? null,
                'shift_name' => $timesheet->shift_name ?? ($convertedTimesheet->shift ? $convertedTimesheet->shift->name : null),
                'checkin_company_address' => $this->formatCompanyAddress($convertedTimesheet->companyAddress ?? null),
                'checkout_company_address' => $this->formatCompanyAddress($convertedTimesheet->checkoutCompanyAddress ?? null),
            ];
        })->toArray();
    }

    /**
     * Tính late minutes
     */
    protected function calculateLateMinutes($timesheet, $scheduleStart): int
    {
        if (!$timesheet->checkin || !$scheduleStart) {
            return 0;
        }

        $checkinTime = Carbon::parse($timesheet->checkin);
        $scheduleStartTime = Carbon::parse($timesheet->date->format('Y-m-d') . ' ' . $scheduleStart);

        return $checkinTime->gt($scheduleStartTime) ? $checkinTime->diffInMinutes($scheduleStartTime) : 0;
    }

    /**
     * Tính early minutes
     */
    protected function calculateEarlyMinutes($timesheet, $scheduleEnd): int
    {
        if (!$timesheet->checkout || !$scheduleEnd) {
            return 0;
        }

        $checkoutTime = Carbon::parse($timesheet->checkout);
        $scheduleEndTime = Carbon::parse($timesheet->date->format('Y-m-d') . ' ' . $scheduleEnd);

        return $checkoutTime->lt($scheduleEndTime) ? $scheduleEndTime->diffInMinutes($checkoutTime) : 0;
    }

    /**
     * Lấy lý do điều chỉnh từ giải trình
     */
    public function getAdjustmentReason(Collection $explanations): ?string
    {
        $approvedExplanations = $explanations->where('final_status', 'approved');

        if ($approvedExplanations->isEmpty()) {
            return null;
        }

        return $approvedExplanations->pluck('explanation')->implode('; ');
    }

    /**
     * Xác định status cuối cùng có xét đến giải trình đã được duyệt
     */
    public function determineFinalStatusWithExplanations(array $attendanceData, Collection $explanations): array
    {
        // Lấy các giải trình đã được duyệt
        $approvedExplanations = $explanations->filter(function($explanation) {
            return $explanation->final_status === 'approved';
        });

        // Nếu không có giải trình được duyệt, giữ nguyên status gốc
        if ($approvedExplanations->isEmpty()) {
            return [
                'status' => $attendanceData['status'],
                'color' => $attendanceData['status_color'],
                'text' => 'Đã giải trình (chờ duyệt)'
            ];
        }

        // Kiểm tra xem có vi phạm nào chưa được che phủ bởi giải trình không
        $hasUncoveredViolations = false;

        // Kiểm tra late
        if (isset($attendanceData['late_minutes']) && $attendanceData['late_minutes'] > 0) {
            $hasLateExplanation = $approvedExplanations->whereIn('explanation_type', [
                'late', 'other'
            ])->isNotEmpty();

            if (!$hasLateExplanation) {
                $hasUncoveredViolations = true;
            }
        }

        // Kiểm tra early
        if (isset($attendanceData['early_minutes']) && $attendanceData['early_minutes'] > 0) {
            $hasEarlyExplanation = $approvedExplanations->whereIn('explanation_type', [
                'early', 'other'
            ])->isNotEmpty();

            if (!$hasEarlyExplanation) {
                $hasUncoveredViolations = true;
            }
        }

        // Kiểm tra missing checkin
        if (!isset($attendanceData['checkin']) || !$attendanceData['checkin']) {
            $hasNoCheckinExplanation = $approvedExplanations->whereIn('explanation_type', [
                'no_checkin', 'other'
            ])->isNotEmpty();

            if (!$hasNoCheckinExplanation) {
                $hasUncoveredViolations = true;
            }
        }

        // Kiểm tra missing checkout
        if (!isset($attendanceData['checkout']) || !$attendanceData['checkout']) {
            $hasNoCheckoutExplanation = $approvedExplanations->whereIn('explanation_type', [
                'no_checkout', 'other'
            ])->isNotEmpty();

            if (!$hasNoCheckoutExplanation) {
                $hasUncoveredViolations = true;
            }
        }

        // Xác định status cuối cùng
        if ($hasUncoveredViolations) {
            return [
                'status' => 'has_issue',
                'color' => 'danger',  // Màu đỏ cho "Có vấn đề"
                'text' => 'Đã giải trình (còn vi phạm)'
            ];
        } else {
            return [
                'status' => 'perfect',
                'color' => 'success',
                'text' => 'Đã giải trình (hoàn tất)'
            ];
        }
    }

    /**
     * Tính toán xem attendance có được điều chỉnh không
     */
    protected function calculateIsAdjusted(Collection $explanations, float $finalWorkday, float $originalWorkday, float $finalOtHours, ?object $approvedShiftChange = null): bool
    {
        // Kiểm tra có giải trình được approved và ảnh hưởng đến workday không
        $hasWorkdayAffectingExplanations = $explanations->filter(function($explanation) {
            return $explanation->final_status === 'approved' &&
                   in_array($explanation->explanation_type, [
                       'late', 'early', 'insufficient_hours', 'no_checkin', 'no_checkout',
                       'remote_work', 'shift_change', 'new_employee_no_account', 'other'
                   ]);
        })->isNotEmpty();

        // Kiểm tra có OT được approved không
        $hasApprovedOt = $finalOtHours > 0;

        // Kiểm tra workday có thay đổi không
        $workdayChanged = abs($finalWorkday - $originalWorkday) > 0.001; // Sử dụng epsilon để tránh lỗi floating point

        // Kiểm tra có shift change được approved không
        $hasApprovedShiftChange = $approvedShiftChange !== null;

        return $hasWorkdayAffectingExplanations || $hasApprovedOt || $workdayChanged || $hasApprovedShiftChange;
    }

    /**
     * Lấy giải trình thay đổi ca được approved (nếu có)
     */
    protected function getApprovedShiftChangeExplanation(Collection $explanations): ?object
    {
        return $explanations->filter(function($explanation) {
            return $explanation->explanation_type === 'shift_change' &&
                   $explanation->final_status === 'approved' &&
                   $explanation->shift_change_id;
        })->first();
    }

    /**
     * Format company address data safely
     */
    protected function formatCompanyAddress($companyAddress)
    {
        if (!$companyAddress) {
            return null;
        }

        try {
            return [
                'id' => $companyAddress->id,
                'name' => $companyAddress->name,
                'lat' => $companyAddress->lat,
                'long' => $companyAddress->long
            ];
        } catch (\Exception $e) {
            Log::warning('Error formatting company address in AttendanceDataProcessor:', [
                'error' => $e->getMessage(),
                'company_address_id' => $companyAddress->id ?? 'unknown'
            ]);
            return null;
        }
    }
}
