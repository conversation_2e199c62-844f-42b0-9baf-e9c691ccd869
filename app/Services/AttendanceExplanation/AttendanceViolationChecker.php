<?php

namespace App\Services\AttendanceExplanation;

use Carbon\Carbon;
use Illuminate\Support\Collection;

class AttendanceViolationChecker
{
    protected AttendanceStatusCalculator $statusCalculator;

    public function __construct(AttendanceStatusCalculator $statusCalculator)
    {
        $this->statusCalculator = $statusCalculator;
    }

    /**
     * Kiểm tra vi phạm cho dữ liệu timesheet đã được gộp
     * 
     * @param array $consolidatedData Dữ liệu từ AttendanceDataProcessor
     * @param Collection $explanations Giải trình (nếu có)
     * @return array
     */
    public function checkViolations(array $consolidatedData, Collection $explanations = null): array
    {
        $explanations = $explanations ?? collect();

        // Lấy thông tin cơ bản từ consolidated data
        $checkin = $consolidatedData['checkin'];
        $checkout = $consolidatedData['checkout'];
        $workingShift = $consolidatedData['working_shift'];
        $date = $consolidatedData['date'];
        $workday = $consolidatedData['workday'];

        // Nếu không có ca làm việc, không thể kiểm tra vi phạm
        if (!$workingShift) {
            return [
                'has_violations' => false,
                'late_minutes' => 0,
                'early_minutes' => 0,
                'missing_checkin' => false,
                'missing_checkout' => false,
                'status' => 'no_data',
                'status_text' => 'Chưa chấm công',
                'status_color' => 'default',
                'violations_summary' => [],
                'final_status' => $this->determineFinalStatusWithExplanations($consolidatedData, $explanations)
            ];
        }

        // Tính toán vi phạm
        $violations = $this->calculateViolations($checkin, $checkout, $workingShift, $date);

        // Xác định status dựa trên vi phạm
        $status = $this->statusCalculator->determineAttendanceStatus(
            $workday, 
            $violations['late_minutes'], 
            $violations['early_minutes'], 
            (object) $consolidatedData
        );

        // Tạo tóm tắt vi phạm
        $violationsSummary = $this->createViolationsSummary($violations);

        // Xác định status cuối cùng có xét đến giải trình
        $finalStatus = $this->determineFinalStatusWithExplanations($consolidatedData, $explanations, $violations);

        return array_merge($violations, [
            'status' => $status,
            'status_text' => $this->statusCalculator->getStatusText($status),
            'status_color' => $this->statusCalculator->getStatusColor($status),
            'violations_summary' => $violationsSummary,
            'final_status' => $finalStatus
        ]);
    }

    /**
     * Tính toán các vi phạm cụ thể
     */
    protected function calculateViolations($checkin, $checkout, $workingShift, $date): array
    {
        $lateMinutes = $this->calculateLateMinutes($checkin, $workingShift, $date);
        $earlyMinutes = $this->calculateEarlyMinutes($checkout, $workingShift, $date);
        $missingCheckin = !$checkin;
        $missingCheckout = !$checkout;

        $hasViolations = $lateMinutes > 0 || $earlyMinutes > 0 || $missingCheckin || $missingCheckout;

        return [
            'has_violations' => $hasViolations,
            'late_minutes' => $lateMinutes,
            'early_minutes' => $earlyMinutes,
            'missing_checkin' => $missingCheckin,
            'missing_checkout' => $missingCheckout,
        ];
    }

    /**
     * Tính late minutes
     */
    protected function calculateLateMinutes($checkin, $workingShift, $date): int
    {
        if (!$checkin || !$workingShift || !$workingShift->start_time) {
            return 0;
        }

        $checkinTime = Carbon::parse($checkin);
        $scheduleStartTime = Carbon::parse($date->format('Y-m-d') . ' ' . $workingShift->start_time);

        return $checkinTime->gt($scheduleStartTime) ? $checkinTime->diffInMinutes($scheduleStartTime) : 0;
    }

    /**
     * Tính early minutes
     */
    protected function calculateEarlyMinutes($checkout, $workingShift, $date): int
    {
        if (!$checkout || !$workingShift || !$workingShift->end_time) {
            return 0;
        }

        $checkoutTime = Carbon::parse($checkout);
        $scheduleEndTime = Carbon::parse($date->format('Y-m-d') . ' ' . $workingShift->end_time);

        return $checkoutTime->lt($scheduleEndTime) ? $scheduleEndTime->diffInMinutes($checkoutTime) : 0;
    }

    /**
     * Tạo tóm tắt vi phạm
     */
    protected function createViolationsSummary(array $violations): array
    {
        $summary = [];

        if ($violations['missing_checkin']) {
            $summary[] = [
                'type' => 'missing_checkin',
                'message' => 'Thiếu checkin',
                'severity' => 'high'
            ];
        }

        if ($violations['missing_checkout']) {
            $summary[] = [
                'type' => 'missing_checkout',
                'message' => 'Thiếu checkout',
                'severity' => 'high'
            ];
        }

        if ($violations['late_minutes'] > 0) {
            $summary[] = [
                'type' => 'late',
                'message' => "Đi muộn {$violations['late_minutes']} phút",
                'severity' => $violations['late_minutes'] > 30 ? 'high' : 'medium',
                'minutes' => $violations['late_minutes']
            ];
        }

        if ($violations['early_minutes'] > 0) {
            $summary[] = [
                'type' => 'early',
                'message' => "Về sớm {$violations['early_minutes']} phút",
                'severity' => $violations['early_minutes'] > 30 ? 'high' : 'medium',
                'minutes' => $violations['early_minutes']
            ];
        }

        return $summary;
    }

    /**
     * Xác định status cuối cùng có xét đến giải trình đã được duyệt
     */
    protected function determineFinalStatusWithExplanations(array $consolidatedData, Collection $explanations, array $violations = []): array
    {
        // Lấy các giải trình đã được duyệt
        $approvedExplanations = $explanations->filter(function($explanation) {
            return $explanation->final_status === 'approved';
        });

        // Nếu không có giải trình được duyệt, giữ nguyên status gốc
        if ($approvedExplanations->isEmpty()) {
            if ($explanations->isNotEmpty()) {
                return [
                    'status' => 'pending_explanation',
                    'color' => 'warning',
                    'text' => 'Đã giải trình (chờ duyệt)'
                ];
            }

            // Không có giải trình, trả về status dựa trên vi phạm
            if (empty($violations) || !$violations['has_violations']) {
                return [
                    'status' => 'perfect',
                    'color' => 'success',
                    'text' => 'Hoàn hảo'
                ];
            } else {
                return [
                    'status' => 'has_issue',
                    'color' => 'danger',
                    'text' => 'Có vi phạm'
                ];
            }
        }

        // Kiểm tra xem có vi phạm nào chưa được che phủ bởi giải trình không
        $hasUncoveredViolations = $this->checkUncoveredViolations($violations, $approvedExplanations);

        // Xác định status cuối cùng
        if ($hasUncoveredViolations) {
            return [
                'status' => 'partial_explained',
                'color' => 'warning',
                'text' => 'Đã giải trình (còn vi phạm)'
            ];
        } else {
            return [
                'status' => 'fully_explained',
                'color' => 'success',
                'text' => 'Đã giải trình (hoàn tất)'
            ];
        }
    }

    /**
     * Kiểm tra vi phạm chưa được che phủ bởi giải trình
     */
    protected function checkUncoveredViolations(array $violations, Collection $approvedExplanations): bool
    {
        if (empty($violations) || !$violations['has_violations']) {
            return false;
        }

        $hasUncoveredViolations = false;

        // Kiểm tra late
        if ($violations['late_minutes'] > 0) {
            $hasLateExplanation = $approvedExplanations->whereIn('explanation_type', [
                'late', 'other'
            ])->isNotEmpty();

            if (!$hasLateExplanation) {
                $hasUncoveredViolations = true;
            }
        }

        // Kiểm tra early
        if ($violations['early_minutes'] > 0) {
            $hasEarlyExplanation = $approvedExplanations->whereIn('explanation_type', [
                'early', 'other'
            ])->isNotEmpty();

            if (!$hasEarlyExplanation) {
                $hasUncoveredViolations = true;
            }
        }

        // Kiểm tra missing checkin
        if ($violations['missing_checkin']) {
            $hasNoCheckinExplanation = $approvedExplanations->whereIn('explanation_type', [
                'no_checkin', 'other'
            ])->isNotEmpty();

            if (!$hasNoCheckinExplanation) {
                $hasUncoveredViolations = true;
            }
        }

        // Kiểm tra missing checkout
        if ($violations['missing_checkout']) {
            $hasNoCheckoutExplanation = $approvedExplanations->whereIn('explanation_type', [
                'no_checkout', 'other'
            ])->isNotEmpty();

            if (!$hasNoCheckoutExplanation) {
                $hasUncoveredViolations = true;
            }
        }

        return $hasUncoveredViolations;
    }

    /**
     * Kiểm tra vi phạm cho multiple days (batch processing)
     */
    public function checkMultipleDaysViolations(array $consolidatedDataArray, array $explanationsArray = []): array
    {
        $results = [];

        foreach ($consolidatedDataArray as $date => $consolidatedData) {
            $explanations = collect($explanationsArray[$date] ?? []);
            $results[$date] = $this->checkViolations($consolidatedData, $explanations);
        }

        return $results;
    }
}
