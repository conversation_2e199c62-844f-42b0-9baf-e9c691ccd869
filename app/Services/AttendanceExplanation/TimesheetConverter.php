<?php

namespace App\Services\AttendanceExplanation;

use App\Models\Timesheet;
use App\Models\Shift;
use App\Models\ShiftRuleHistory;
use Carbon\Carbon;

class TimesheetConverter
{
    /**
     * Convert stdClass object từ export query thành Timesheet model
     */
    public function convertFromStdClass($stdClassTimesheet): Timesheet
    {
        // Nếu đã là Timesheet model thì return luôn
        if ($stdClassTimesheet instanceof Timesheet) {
            return $stdClassTimesheet;
        }

        // Nếu không phải stdClass thì tìm Timesheet model thực tế từ database
        if (!is_object($stdClassTimesheet) || get_class($stdClassTimesheet) !== 'stdClass') {
            throw new \InvalidArgumentException('Expected stdClass or Timesheet instance');
        }

        // Thử tìm Timesheet model thực tế từ database trước
        $actualTimesheet = $this->findActualTimesheet($stdClassTimesheet);
        if ($actualTimesheet) {
            return $actualTimesheet;
        }

        // Nếu không tìm thấy, tạo Timesheet model tạm thời từ stdClass data
        return $this->createTimesheetFromStdClass($stdClassTimesheet);
    }

    /**
     * Tìm Timesheet model thực tế từ database
     */
    protected function findActualTimesheet($stdClassTimesheet): ?Timesheet
    {
        if (!isset($stdClassTimesheet->user_id) || !isset($stdClassTimesheet->date)) {
            return null;
        }

        try {
            return Timesheet::where('user_id', $stdClassTimesheet->user_id)
                ->whereDate('date', $stdClassTimesheet->date)
                ->with(['shift', 'shiftRuleHistory'])
                ->first();
        } catch (\Exception $e) {
            // Log error và return null thay vì throw exception
            \Log::warning('Error finding actual timesheet in TimesheetConverter:', [
                'error' => $e->getMessage(),
                'user_id' => $stdClassTimesheet->user_id,
                'date' => $stdClassTimesheet->date
            ]);
            return null;
        }
    }

    /**
     * Tạo một Timesheet model tạm thời từ stdClass data (cho export)
     */
    protected function createTimesheetFromStdClass($stdClassTimesheet): Timesheet
    {
        $timesheet = new Timesheet();

        // Copy các thuộc tính cơ bản với error handling
        $timesheet->id = $stdClassTimesheet->id ?? null;
        $timesheet->user_id = $stdClassTimesheet->user_id ?? null;

        // Xử lý date an toàn
        if (isset($stdClassTimesheet->date)) {
            try {
                $timesheet->date = Carbon::parse($stdClassTimesheet->date);
            } catch (\Exception $e) {
                $timesheet->date = Carbon::today();
            }
        } else {
            $timesheet->date = Carbon::today();
        }

        // Xử lý checkin/checkout an toàn
        try {
            $timesheet->checkin = isset($stdClassTimesheet->checkin) && $stdClassTimesheet->checkin
                ? Carbon::parse($stdClassTimesheet->checkin)
                : null;
        } catch (\Exception $e) {
            $timesheet->checkin = null;
        }

        try {
            $timesheet->checkout = isset($stdClassTimesheet->checkout) && $stdClassTimesheet->checkout
                ? Carbon::parse($stdClassTimesheet->checkout)
                : null;
        } catch (\Exception $e) {
            $timesheet->checkout = null;
        }

        $timesheet->shift_id = $stdClassTimesheet->shift_id ?? null;
        $timesheet->workday = $stdClassTimesheet->workday ?? 0;

        // Tạo shift object nếu có thông tin shift
        if (isset($stdClassTimesheet->shift_name)) {
            try {
                $shift = $this->createShiftFromStdClass($stdClassTimesheet);
                $timesheet->setRelation('shift', $shift);
            } catch (\Exception $e) {
                // Log error nhưng không throw exception
                \Log::warning('Error creating shift from stdClass in TimesheetConverter:', [
                    'error' => $e->getMessage(),
                    'timesheet_id' => $stdClassTimesheet->id ?? 'unknown'
                ]);
            }
        }

        // Tạo shift rule history object nếu có thông tin
        if (isset($stdClassTimesheet->shift_break_times)) {
            try {
                $shiftRule = $this->createShiftRuleFromStdClass($stdClassTimesheet);
                $timesheet->setRelation('shiftRuleHistory', $shiftRule);
            } catch (\Exception $e) {
                // Log error nhưng không throw exception
                \Log::warning('Error creating shift rule from stdClass in TimesheetConverter:', [
                    'error' => $e->getMessage(),
                    'timesheet_id' => $stdClassTimesheet->id ?? 'unknown'
                ]);
            }
        }

        return $timesheet;
    }

    /**
     * Tạo Shift object từ stdClass data
     */
    protected function createShiftFromStdClass($stdClassTimesheet): Shift
    {
        $shift = new Shift();
        $shift->id = $stdClassTimesheet->shift_id ?? null;
        $shift->name = $stdClassTimesheet->shift_name ?? 'Unknown Shift';
        $shift->start_time = $stdClassTimesheet->shift_start_time ?? '08:00:00';
        $shift->end_time = $stdClassTimesheet->shift_end_time ?? '17:00:00';
        $shift->workday_min_1 = $stdClassTimesheet->shift_workday_min_1 ?? 7.5;
        $shift->workday_min_2 = $stdClassTimesheet->shift_workday_min_2 ?? 4.5;
        $shift->default_workday_threshold = $stdClassTimesheet->shift_default_workday_threshold ?? 1.0;

        return $shift;
    }

    /**
     * Tạo ShiftRuleHistory object từ stdClass data
     */
    protected function createShiftRuleFromStdClass($stdClassTimesheet): ShiftRuleHistory
    {
        $shiftRule = new ShiftRuleHistory();
        $shiftRule->id = $stdClassTimesheet->shift_rule_history_id ?? null;
        $shiftRule->shift_id = $stdClassTimesheet->shift_id ?? null;
        $shiftRule->start_time = $stdClassTimesheet->shift_start_time ?? '08:00:00';
        $shiftRule->end_time = $stdClassTimesheet->shift_end_time ?? '17:00:00';
        $shiftRule->workday_min_1 = $stdClassTimesheet->shift_workday_min_1 ?? 7.5;
        $shiftRule->workday_min_2 = $stdClassTimesheet->shift_workday_min_2 ?? 4.5;
        $shiftRule->break_times = $stdClassTimesheet->shift_break_times ?? '[]';

        return $shiftRule;
    }

    /**
     * Kiểm tra xem object có cần convert không
     */
    public function needsConversion($timesheet): bool
    {
        return is_object($timesheet) && get_class($timesheet) === 'stdClass';
    }

    /**
     * Convert collection of timesheets
     */
    public function convertCollection($timesheets): \Illuminate\Support\Collection
    {
        return collect($timesheets)->map(function ($timesheet) {
            return $this->convertFromStdClass($timesheet);
        });
    }
}
