<?php

namespace App\Services\AttendanceExplanation\Processors;

use App\Services\AttendanceExplanation\BaseExplanationProcessor;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class NoCheckinCheckoutExplanationProcessor extends BaseExplanationProcessor
{
    /**
     * Xử lý dữ liệu chấm công cho explanation-only row (không có timesheet)
     *
     * @param array $baseData
     * @param object $explanation
     * @return array
     */
    public function processExplanationOnlyRow(array $baseData, object $explanation): array
    {
        $attendanceData = $this->createBaseAttendanceData();
        
        // Giải trình quên checkin/checkout không có timesheet - gi<PERSON> đ<PERSON>nh ca HC
        $attendanceData['workday'] = 1.0;
        $attendanceData['attendance_status'] = 'Có giải trình';
        $attendanceData['explanation'] = $explanation->explanation ?? '';
        
        return $attendanceData;
    }

    /**
     * Áp dụng explanation vào dữ liệu chấm công có timesheet
     *
     * @param array $attendanceData
     * @param Collection $explanations
     * @param object|null $timesheet
     * @return array
     */
    public function applyToAttendanceData(array $attendanceData, Collection $explanations, ?object $timesheet = null): array
    {
        $noCheckinCheckoutExplanations = $explanations->whereIn('explanation_type', ['no_checkin', 'no_checkout']);
        
        if ($noCheckinCheckoutExplanations->isEmpty()) {
            return $attendanceData;
        }

        // Cập nhật explanation summary
        $currentSummary = $attendanceData['explanation_summary'] ?? '';
        $explanationText = $this->getExplanationSummary($noCheckinCheckoutExplanations);
        $attendanceData['explanation_summary'] = $currentSummary ? $currentSummary . '; ' . $explanationText : $explanationText;
        
        // Nếu có giải trình quên checkin/checkout được duyệt
        if ($this->hasApprovedExplanation($noCheckinCheckoutExplanations)) {
            // Điều chỉnh workday theo threshold của shift
            $attendanceData = $this->adjustWorkdayForApprovedExplanation($attendanceData, $timesheet);
            
            // Cập nhật attendance status
            $attendanceData['attendance_status'] = 'has_explanation';
            
            // Xử lý riêng cho từng loại
            $this->processSpecificExplanationTypes($attendanceData, $noCheckinCheckoutExplanations);
        }

        return $attendanceData;
    }

    /**
     * Tính toán workday cho explanation type này
     *
     * @param object|null $timesheet
     * @param object $explanation
     * @return float
     */
    public function calculateWorkday(?object $timesheet, object $explanation): float
    {
        if (!$timesheet) {
            // Không có timesheet - giả định ca HC
            return 1.0;
        }

        // Có timesheet - sử dụng threshold từ shift
        return $timesheet->shift_default_workday_threshold ?? 1.0;
    }

    /**
     * Xác định attendance status cho explanation type này
     *
     * @param object|null $timesheet
     * @param object $explanation
     * @return string
     */
    public function determineAttendanceStatus(?object $timesheet, object $explanation): string
    {
        if (!$timesheet) {
            return 'Có giải trình';
        }

        return 'has_explanation';
    }

    /**
     * Kiểm tra explanation type này có ảnh hưởng đến penalty không
     *
     * @return bool
     */
    public function affectsPenalty(): bool
    {
        return true; // No checkin/checkout explanation ảnh hưởng đến penalty
    }

    /**
     * Kiểm tra có explanation được duyệt không
     *
     * @param Collection $explanations
     * @return bool
     */
    private function hasApprovedExplanation(Collection $explanations): bool
    {
        return $explanations->where('final_status', 'approved')->isNotEmpty();
    }

    /**
     * Điều chỉnh workday cho explanation được duyệt
     *
     * @param array $attendanceData
     * @param object|null $timesheet
     * @return array
     */
    private function adjustWorkdayForApprovedExplanation(array $attendanceData, ?object $timesheet): array
    {
        if (!$timesheet) {
            $attendanceData['final_workday'] = 1.0;
            $attendanceData['total_workday'] = 1.0;
            return $attendanceData;
        }

        // Sử dụng threshold từ shift
        $adjustedWorkday = $timesheet->shift_default_workday_threshold ?? 1.0;
        $attendanceData['final_workday'] = $adjustedWorkday;
        $attendanceData['total_workday'] = $adjustedWorkday;
        
        return $attendanceData;
    }

    /**
     * Xử lý riêng cho từng loại explanation
     *
     * @param array &$attendanceData
     * @param Collection $explanations
     * @return void
     */
    private function processSpecificExplanationTypes(array &$attendanceData, Collection $explanations): void
    {
        $noCheckinExplanations = $explanations->where('explanation_type', 'no_checkin');
        $noCheckoutExplanations = $explanations->where('explanation_type', 'no_checkout');

        if ($noCheckinExplanations->isNotEmpty()) {
            $attendanceData['late_explanation'] = $this->getExplanationSummary($noCheckinExplanations);
        }

        if ($noCheckoutExplanations->isNotEmpty()) {
            $attendanceData['early_explanation'] = $this->getExplanationSummary($noCheckoutExplanations);
        }
    }
}
