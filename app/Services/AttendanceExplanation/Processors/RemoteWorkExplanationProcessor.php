<?php

namespace App\Services\AttendanceExplanation\Processors;

use App\Services\AttendanceExplanation\BaseExplanationProcessor;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class RemoteWorkExplanationProcessor extends BaseExplanationProcessor
{
    /**
     * Xử lý dữ liệu chấm công cho explanation-only row (không có timesheet)
     *
     * @param array $baseData
     * @param object $explanation
     * @return array
     */
    public function processExplanationOnlyRow(array $baseData, object $explanation): array
    {
        $attendanceData = $this->createBaseAttendanceData();
        
        // Lấy workday từ remote_shift_id
        $workday = $this->getWorkdayFromShift($explanation->remote_shift_id);
        $attendanceData['workday'] = $workday;
        $attendanceData['attendance_status'] = 'Làm việc từ xa';
        $attendanceData['explanation'] = $explanation->explanation ?? '';
        
        return $attendanceData;
    }

    /**
     * Áp dụng explanation vào dữ liệu chấm công có timesheet
     *
     * @param array $attendanceData
     * @param Collection $explanations
     * @param object|null $timesheet
     * @return array
     */
    public function applyToAttendanceData(array $attendanceData, Collection $explanations, ?object $timesheet = null): array
    {
        $remoteWorkExplanations = $explanations->where('explanation_type', 'remote_work');
        
        if ($remoteWorkExplanations->isEmpty()) {
            return $attendanceData;
        }

        // Cập nhật explanation summary
        $currentSummary = $attendanceData['explanation_summary'] ?? '';
        $explanationText = $this->getRemoteWorkExplanationSummary($remoteWorkExplanations);
        $attendanceData['explanation_summary'] = $currentSummary ? $currentSummary . '; ' . $explanationText : $explanationText;
        
        // Nếu có giải trình remote work được duyệt
        if ($this->hasApprovedExplanation($remoteWorkExplanations)) {
            // Điều chỉnh workday theo remote shift
            $attendanceData = $this->adjustWorkdayForRemoteWork($attendanceData, $remoteWorkExplanations);
            
            // Cập nhật attendance status
            $attendanceData['attendance_status'] = 'has_explanation';
        }

        return $attendanceData;
    }

    /**
     * Tính toán workday cho explanation type này
     *
     * @param object|null $timesheet
     * @param object $explanation
     * @return float
     */
    public function calculateWorkday(?object $timesheet, object $explanation): float
    {
        // Lấy workday từ remote_shift_id
        return $this->getWorkdayFromShift($explanation->remote_shift_id);
    }

    /**
     * Xác định attendance status cho explanation type này
     *
     * @param object|null $timesheet
     * @param object $explanation
     * @return string
     */
    public function determineAttendanceStatus(?object $timesheet, object $explanation): string
    {
        if (!$timesheet) {
            return 'Làm việc từ xa';
        }

        return 'has_explanation';
    }

    /**
     * Kiểm tra explanation type này có ảnh hưởng đến penalty không
     *
     * @return bool
     */
    public function affectsPenalty(): bool
    {
        return false; // Remote work không ảnh hưởng đến penalty
    }

    /**
     * Kiểm tra có explanation được duyệt không
     *
     * @param Collection $explanations
     * @return bool
     */
    private function hasApprovedExplanation(Collection $explanations): bool
    {
        return $explanations->where('final_status', 'approved')->isNotEmpty();
    }

    /**
     * Điều chỉnh workday cho remote work
     *
     * @param array $attendanceData
     * @param Collection $remoteWorkExplanations
     * @return array
     */
    private function adjustWorkdayForRemoteWork(array $attendanceData, Collection $remoteWorkExplanations): array
    {
        // Lấy explanation đầu tiên được duyệt
        $approvedExplanation = $remoteWorkExplanations->where('final_status', 'approved')->first();
        
        if (!$approvedExplanation) {
            return $attendanceData;
        }

        // Lấy workday từ remote shift
        $remoteWorkday = $this->getWorkdayFromShift($approvedExplanation->remote_shift_id);
        
        $attendanceData['final_workday'] = $remoteWorkday;
        $attendanceData['total_workday'] = $remoteWorkday;
        
        return $attendanceData;
    }

    /**
     * Lấy explanation summary cho remote work với thông tin shift
     *
     * @param Collection $remoteWorkExplanations
     * @return string
     */
    private function getRemoteWorkExplanationSummary(Collection $remoteWorkExplanations): string
    {
        if ($remoteWorkExplanations->isEmpty()) {
            return '';
        }

        $summaries = [];
        foreach ($remoteWorkExplanations as $explanation) {
            $text = $explanation->explanation ?? '';
            $shiftInfo = '';
            
            if ($explanation->remote_shift_id) {
                $shift = \App\Models\Shift::find($explanation->remote_shift_id);
                $shiftInfo = $shift ? " (Ca: {$shift->name})" : '';
            }
            
            $summaries[] = "Remote work{$shiftInfo}: {$text}";
        }

        return implode('; ', $summaries);
    }
}
