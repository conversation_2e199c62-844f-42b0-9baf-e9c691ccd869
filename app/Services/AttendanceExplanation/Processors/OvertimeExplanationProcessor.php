<?php

namespace App\Services\AttendanceExplanation\Processors;

use App\Services\AttendanceExplanation\BaseExplanationProcessor;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class OvertimeExplanationProcessor extends BaseExplanationProcessor
{
    /**
     * Xử lý dữ liệu chấm công cho explanation-only row (không có timesheet)
     *
     * @param array $baseData
     * @param object $explanation
     * @return array
     */
    public function processExplanationOnlyRow(array $baseData, object $explanation): array
    {
        $attendanceData = $this->createBaseAttendanceData();
        
        // Giải trình OT không có timesheet = không tính công
        $attendanceData['workday'] = 0;
        $attendanceData['overtime_hours'] = floatval($explanation->ot_hours ?? 0);
        $attendanceData['attendance_status'] = 'Chỉ OT';
        $attendanceData['explanation'] = $explanation->explanation ?? '';
        
        return $attendanceData;
    }

    /**
     * Áp dụng explanation vào dữ liệu chấm công có timesheet
     *
     * @param array $attendanceData
     * @param Collection $explanations
     * @param object|null $timesheet
     * @return array
     */
    public function applyToAttendanceData(array $attendanceData, Collection $explanations, ?object $timesheet = null): array
    {
        $overtimeExplanations = $explanations->where('explanation_type', 'overtime');
        
        if ($overtimeExplanations->isEmpty()) {
            return $attendanceData;
        }

        // Tính tổng OT hours từ tất cả explanations
        $totalOtHours = $overtimeExplanations->sum('ot_hours');
        $attendanceData['overtime_hours'] = $totalOtHours;

        // Cập nhật explanation summary
        $currentSummary = $attendanceData['explanation_summary'] ?? '';
        $explanationText = $this->getOvertimeExplanationSummary($overtimeExplanations);
        $attendanceData['explanation_summary'] = $currentSummary ? $currentSummary . '; ' . $explanationText : $explanationText;
        
        // OT không ảnh hưởng đến workday chính, chỉ thêm OT hours
        // Không thay đổi final_workday hoặc total_workday

        return $attendanceData;
    }

    /**
     * Tính toán workday cho explanation type này
     *
     * @param object|null $timesheet
     * @param object $explanation
     * @return float
     */
    public function calculateWorkday(?object $timesheet, object $explanation): float
    {
        // OT không ảnh hưởng đến workday, chỉ thêm giờ OT
        return 0;
    }

    /**
     * Xác định attendance status cho explanation type này
     *
     * @param object|null $timesheet
     * @param object $explanation
     * @return string
     */
    public function determineAttendanceStatus(?object $timesheet, object $explanation): string
    {
        if (!$timesheet) {
            return 'Chỉ OT';
        }

        // Có timesheet - không thay đổi status, chỉ thêm OT
        return 'normal';
    }

    /**
     * Kiểm tra explanation type này có ảnh hưởng đến penalty không
     *
     * @return bool
     */
    public function affectsPenalty(): bool
    {
        return false; // OT không ảnh hưởng đến penalty
    }

    /**
     * Lấy explanation summary cho OT với thông tin giờ OT
     *
     * @param Collection $overtimeExplanations
     * @return string
     */
    private function getOvertimeExplanationSummary(Collection $overtimeExplanations): string
    {
        if ($overtimeExplanations->isEmpty()) {
            return '';
        }

        $summaries = [];
        foreach ($overtimeExplanations as $explanation) {
            $otHours = $explanation->ot_hours ?? 0;
            $text = $explanation->explanation ?? '';
            $summaries[] = "OT {$otHours}h: {$text}";
        }

        return implode('; ', $summaries);
    }
}
