<?php

namespace App\Services\AttendanceExplanation\Processors;

use App\Services\AttendanceExplanation\BaseExplanationProcessor;
use App\Services\Timesheet\ShiftLogicType\LogicTypeFactory;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class ShiftChangeExplanationProcessor extends BaseExplanationProcessor
{
    /**
     * Xử lý dữ liệu chấm công cho explanation-only row (không có timesheet)
     *
     * @param array $baseData
     * @param object $explanation
     * @return array
     */
    public function processExplanationOnlyRow(array $baseData, object $explanation): array
    {
        $attendanceData = $this->createBaseAttendanceData();
        
        // Giải trình đổi ca không có timesheet = không thể đổi ca
        $attendanceData['workday'] = 0;
        $attendanceData['attendance_status'] = 'Giải trình đổi ca';
        $attendanceData['explanation'] = $explanation->explanation ?? '';
        
        return $attendanceData;
    }

    /**
     * Áp dụng explanation vào dữ liệu chấm công có timesheet
     *
     * @param array $attendanceData
     * @param Collection $explanations
     * @param object|null $timesheet
     * @return array
     */
    public function applyToAttendanceData(array $attendanceData, Collection $explanations, ?object $timesheet = null): array
    {
        $shiftChangeExplanations = $explanations->where('explanation_type', 'shift_change');
        
        if ($shiftChangeExplanations->isEmpty()) {
            return $attendanceData;
        }

        // Cập nhật explanation summary
        $currentSummary = $attendanceData['explanation_summary'] ?? '';
        $explanationText = $this->getShiftChangeExplanationSummary($shiftChangeExplanations);
        $attendanceData['explanation_summary'] = $currentSummary ? $currentSummary . '; ' . $explanationText : $explanationText;
        
        // Nếu có giải trình shift change được duyệt
        if ($this->hasApprovedExplanation($shiftChangeExplanations)) {
            // Tính lại workday với ca mới
            $attendanceData = $this->recalculateWorkdayWithNewShift($attendanceData, $shiftChangeExplanations, $timesheet);
            
            // Cập nhật attendance status
            $attendanceData['attendance_status'] = 'has_explanation';
        }

        return $attendanceData;
    }

    /**
     * Tính toán workday cho explanation type này
     *
     * @param object|null $timesheet
     * @param object $explanation
     * @return float
     */
    public function calculateWorkday(?object $timesheet, object $explanation): float
    {
        if (!$timesheet) {
            // Không có timesheet - không thể đổi ca
            return 0;
        }

        // Có timesheet - tính workday với ca mới
        return $this->calculateWorkdayWithNewShift($timesheet, $explanation);
    }

    /**
     * Xác định attendance status cho explanation type này
     *
     * @param object|null $timesheet
     * @param object $explanation
     * @return string
     */
    public function determineAttendanceStatus(?object $timesheet, object $explanation): string
    {
        if (!$timesheet) {
            return 'Giải trình đổi ca';
        }

        return 'has_explanation';
    }

    /**
     * Kiểm tra explanation type này có ảnh hưởng đến penalty không
     *
     * @return bool
     */
    public function affectsPenalty(): bool
    {
        return false; // Shift change không ảnh hưởng đến penalty khi được duyệt
    }

    /**
     * Kiểm tra có explanation được duyệt không
     *
     * @param Collection $explanations
     * @return bool
     */
    private function hasApprovedExplanation(Collection $explanations): bool
    {
        return $explanations->where('final_status', 'approved')->isNotEmpty();
    }

    /**
     * Tính lại workday với ca mới
     *
     * @param array $attendanceData
     * @param Collection $shiftChangeExplanations
     * @param object|null $timesheet
     * @return array
     */
    private function recalculateWorkdayWithNewShift(array $attendanceData, Collection $shiftChangeExplanations, ?object $timesheet): array
    {
        if (!$timesheet) {
            return $attendanceData;
        }

        // Lấy explanation đầu tiên được duyệt
        $approvedExplanation = $shiftChangeExplanations->where('final_status', 'approved')->first();
        
        if (!$approvedExplanation || !$approvedExplanation->shift_change_id) {
            return $attendanceData;
        }

        // Tính workday với ca mới
        $newWorkday = $this->calculateWorkdayWithNewShift($timesheet, $approvedExplanation);
        
        $attendanceData['final_workday'] = $newWorkday;
        $attendanceData['total_workday'] = $newWorkday;
        
        return $attendanceData;
    }

    /**
     * Tính workday với ca mới
     *
     * @param object $timesheet
     * @param object $explanation
     * @return float
     */
    private function calculateWorkdayWithNewShift(object $timesheet, object $explanation): float
    {
        if (!$explanation->shift_change_id) {
            return 0;
        }

        $newShift = \App\Models\Shift::find($explanation->shift_change_id);
        if (!$newShift) {
            return 0;
        }

        // Sử dụng LogicTypeFactory để tính workday với ca mới
        return LogicTypeFactory::create()->calcWorkday([
            'schedule_start_time' => $newShift->start_time,
            'schedule_end_time' => $newShift->end_time,
            'start_time' => $timesheet->checkin?->format('H:i:s'),
            'end_time' => $timesheet->checkout?->format('H:i:s'),
            'workday_min_1' => $newShift->workday_min_1,
            'workday_min_2' => $newShift->workday_min_2,
            'break_times' => $this->parseBreakTimes($newShift->break_times),
        ]);
    }

    /**
     * Lấy explanation summary cho shift change với thông tin ca mới
     *
     * @param Collection $shiftChangeExplanations
     * @return string
     */
    private function getShiftChangeExplanationSummary(Collection $shiftChangeExplanations): string
    {
        if ($shiftChangeExplanations->isEmpty()) {
            return '';
        }

        $summaries = [];
        foreach ($shiftChangeExplanations as $explanation) {
            $text = $explanation->explanation ?? '';
            $shiftInfo = '';
            
            if ($explanation->shift_change_id) {
                $shift = \App\Models\Shift::find($explanation->shift_change_id);
                $shiftInfo = $shift ? " (Ca mới: {$shift->name})" : '';
            }
            
            $summaries[] = "Đổi ca{$shiftInfo}: {$text}";
        }

        return implode('; ', $summaries);
    }
}
