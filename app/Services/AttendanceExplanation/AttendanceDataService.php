<?php

namespace App\Services\AttendanceExplanation;

use App\Models\User;
use App\Models\Timesheet;
use App\Models\AttendanceExplanation;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class AttendanceDataService
{
    protected TimesheetConverter $converter;
    protected AttendanceStatusCalculator $statusCalculator;
    protected WorkdayCalculator $workdayCalculator;
    protected AttendanceDataProcessor $dataProcessor;
    protected AttendanceShiftMismatchService $shiftMismatchService;

    public function __construct(
        TimesheetConverter $converter = null,
        AttendanceStatusCalculator $statusCalculator = null,
        WorkdayCalculator $workdayCalculator = null,
        AttendanceDataProcessor $dataProcessor = null,
        AttendanceShiftMismatchService $shiftMismatchService = null
    ) {
        $this->converter = $converter ?? new TimesheetConverter();
        $this->workdayCalculator = $workdayCalculator ?? new WorkdayCalculator($this->converter);
        $this->statusCalculator = $statusCalculator ?? new AttendanceStatusCalculator($this->converter);
        $this->shiftMismatchService = $shiftMismatchService ?? new AttendanceShiftMismatchService();
        $this->dataProcessor = $dataProcessor ?? new AttendanceDataProcessor(
            $this->converter,
            $this->statusCalculator,
            $this->workdayCalculator
        );
    }

    /**
     * Lấy dữ liệu chấm công cho tháng (cho API - chỉ ngày trong tháng)
     */
    public function getMonthlyAttendance(User $user, Carbon $month): Collection
    {
        // Chỉ lấy range trong tháng hiện tại (cho API)
        $startDate = $month->copy()->startOfMonth();
        $endDate = $month->copy()->endOfMonth();

        return $this->getAttendanceDataInRange($user, $startDate, $endDate);
    }

    /**
     * Lấy dữ liệu chấm công cho calendar view (bao gồm các ngày của tháng trước/sau)
     */
    public function getMonthlyAttendanceForCalendar(User $user, Carbon $month): Collection
    {
        // Lấy range cho toàn bộ calendar (bao gồm ngày của tháng trước/sau)
        $startDate = $month->copy()->startOfMonth()->startOfWeek(Carbon::MONDAY);
        $endDate = $month->copy()->endOfMonth()->endOfWeek(Carbon::SUNDAY);

        return $this->getAttendanceDataInRange($user, $startDate, $endDate);
    }

    /**
     * Lấy dữ liệu chấm công trong khoảng thời gian
     */
    public function getAttendanceDataInRange(User $user, Carbon $startDate, Carbon $endDate): Collection
    {
        // Lấy tất cả timesheets trong khoảng thời gian (giữ tất cả để hỗ trợ nhiều checkin/checkout)
        $timesheets = Timesheet::where('user_id', $user->id)
            ->whereBetween('date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->with(['shift', 'shiftRuleHistory', 'companyAddress', 'checkoutCompanyAddress'])
            ->orderBy('date')
            ->orderBy('checkin') // Sắp xếp theo thời gian checkin
            ->get()
            ->groupBy(function($item) {
                return $item->date->format('Y-m-d');
            });

        // Lấy tất cả giải trình trong tháng với relationships
        $explanations = AttendanceExplanation::where('user_id', $user->id)
            ->whereBetween('date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->with(['remoteShift', 'managerApprover', 'hrApprover', 'taggedUser', 'shiftChange'])
            ->get()
            ->groupBy(function($item) {
                return $item->date->format('Y-m-d');
            });

        // Lấy tổng số giờ OT đã được duyệt cho từng ngày
        $approvedOtHours = AttendanceExplanation::where('user_id', $user->id)
            ->whereBetween('date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->where('explanation_type', 'overtime')
            ->where('manager_status', 'approved')
            ->where('hr_status', 'approved')
            ->whereNotNull('ot_hours')
            ->selectRaw('date, SUM(ot_hours) as total_ot_hours')
            ->groupBy('date')
            ->pluck('total_ot_hours', 'date');

        // Tạo collection cho tất cả ngày trong tháng
        $result = collect();
        $currentDate = $startDate->copy();

        while ($currentDate->lte($endDate)) {
            $dateKey = $currentDate->format('Y-m-d');
            $dayTimesheets = $timesheets->get($dateKey, collect());
            $dayExplanations = $explanations->get($dateKey, collect());

            // Lấy số giờ OT đã được duyệt cho ngày này
            $approvedOtForDay = $approvedOtHours->get($dateKey, 0);

            if ($dayTimesheets->isNotEmpty()) {
                // Có dữ liệu timesheet - xử lý nhiều checkin/checkout
                $attendanceData = $this->dataProcessor->calculateMultipleTimesheetsStatus($dayTimesheets, $dayExplanations);
                $attendanceData['explanations'] = $dayExplanations;
                $attendanceData['has_explanation'] = $dayExplanations->count() > 0;
                $attendanceData['explanation_count'] = $dayExplanations->count();

                // Debug logging
                if ($dayExplanations->where('final_status', 'approved')->isNotEmpty()) {
                    \Log::info('AttendanceDataService - After calculateMultipleTimesheetsStatus', [
                        'date' => $dateKey,
                        'original_workday' => $attendanceData['original_workday'] ?? 'not_set',
                        'workday' => $attendanceData['workday'] ?? 'not_set',
                        'is_adjusted' => $attendanceData['is_adjusted'] ?? 'not_set',
                        'approved_explanations_count' => $dayExplanations->where('final_status', 'approved')->count()
                    ]);
                }

                // Kiểm tra shift mismatch
                $shiftMismatch = $this->shiftMismatchService->checkShiftMismatch($dayTimesheets, $dayExplanations);
                $attendanceData['shift_mismatch'] = $shiftMismatch;

                // === SỬ DỤNG ATTENDANCEVIOLATIONCHECKER ĐỂ KIỂM TRA VI PHẠM ===
                // Kiểm tra vi phạm bằng service riêng biệt
                $violationChecker = app(\App\Services\AttendanceExplanation\AttendanceViolationChecker::class);
                $violationResult = $violationChecker->checkViolations($attendanceData, $dayExplanations);

                // Merge violation results vào attendance data
                $attendanceData = array_merge($attendanceData, [
                    'has_violations' => $violationResult['has_violations'],
                    'late_minutes' => $violationResult['late_minutes'],
                    'early_minutes' => $violationResult['early_minutes'],
                    'missing_checkin' => $violationResult['missing_checkin'],
                    'missing_checkout' => $violationResult['missing_checkout'],
                    'violations_summary' => $violationResult['violations_summary'],
                    'status' => $violationResult['status'],
                    'status_text' => $violationResult['status_text'],
                    'status_color' => $violationResult['status_color'],
                    'final_status' => $violationResult['final_status']
                ]);

                // Cập nhật status color dựa trên trạng thái điều chỉnh và giải trình
                // Ưu tiên: 1. Đã điều chỉnh, 2. Có vấn đề (has_issue), 3. Shift mismatch, 4. Giải trình khác
                if ($attendanceData['is_adjusted']) {
                    $attendanceData['status_color'] = 'success';
                    $attendanceData['status_text'] = 'Đã điều chỉnh';
                } elseif ($violationResult['has_violations']) {
                    // Sử dụng final status từ violation checker
                    $attendanceData['status'] = $violationResult['final_status']['status'];
                    $attendanceData['status_color'] = $violationResult['final_status']['color'];
                    $attendanceData['status_text'] = $violationResult['final_status']['text'];
                } elseif ($shiftMismatch && $shiftMismatch['has_mismatch']) {
                    // Chỉ hiển thị shift mismatch khi không có vấn đề nghiêm trọng khác
                    $attendanceData['status'] = 'shift_mismatch';
                    $attendanceData['status_color'] = 'warning';
                    $attendanceData['status_text'] = 'Lựa chọn sai ca làm việc';
                } else {
                    // Không có vi phạm và không có shift mismatch
                    $attendanceData['status'] = 'perfect';
                    $attendanceData['status_color'] = 'success';
                    $attendanceData['status_text'] = 'Hoàn hảo';
                }
            } else {
                // Không có dữ liệu timesheet
                $finalOtHours = $this->workdayCalculator->getFinalOtHours($dayExplanations);

                $attendanceData = [
                    'date' => $dateKey,
                    'checkin' => null,
                    'checkout' => null,
                    'shift_name' => null,
                    'schedule_start' => null,
                    'schedule_end' => null,
                    'original_workday' => 0,
                    'workday' => 0,
                    'work_hours' => 0,
                    'real_hours' => 0,
                    'real_hours_formatted' => '0 phút',
                    'is_full_workday' => false, // Không có dữ liệu = không làm đủ công
                    // Thêm các field vi phạm cho consistency
                    'has_violations' => true, // Không có dữ liệu = vi phạm
                    'late_minutes' => 0,
                    'early_minutes' => 0,
                    'missing_checkin' => true,
                    'missing_checkout' => true,
                    'violations_summary' => [
                        ['type' => 'missing_checkin', 'message' => 'Thiếu checkin', 'severity' => 'high'],
                        ['type' => 'missing_checkout', 'message' => 'Thiếu checkout', 'severity' => 'high']
                    ],
                    'status' => 'no_data',
                    'status_text' => $dayExplanations->count() > 0 ? 'Đã giải trình' : 'Chưa chấm công',
                    'status_color' => $dayExplanations->count() > 0 ? 'info' : 'default',
                    'final_status' => [
                        'status' => 'no_data',
                        'color' => $dayExplanations->count() > 0 ? 'info' : 'default',
                        'text' => $dayExplanations->count() > 0 ? 'Đã giải trình' : 'Chưa chấm công'
                    ],
                    'explanations' => $dayExplanations,
                    'has_explanation' => $dayExplanations->count() > 0,
                    'explanation_count' => $dayExplanations->count(),
                    'approved_ot_hours' => $finalOtHours,
                    'is_adjusted' => $finalOtHours > 0,
                    'adjustment_reason' => $this->dataProcessor->getAdjustmentReason($dayExplanations),
                ];
            }

            // Debug logging cuối cùng
            if ($dayExplanations->where('final_status', 'approved')->isNotEmpty()) {
                \Log::info('AttendanceDataService - Final result', [
                    'date' => $dateKey,
                    'final_workday' => $attendanceData['workday'] ?? 'not_set',
                    'final_is_adjusted' => $attendanceData['is_adjusted'] ?? 'not_set',
                    'final_status' => $attendanceData['status'] ?? 'not_set',
                    'final_status_text' => $attendanceData['status_text'] ?? 'not_set'
                ]);
            }

            $result->put($dateKey, $attendanceData);
            $currentDate->addDay();
        }

        return $result;
    }

    /**
     * Lấy thông tin chấm công cho một ngày cụ thể
     */
    public function getDayAttendance(User $user, Carbon $date): ?array
    {
        $dateKey = $date->format('Y-m-d');

        // Lấy tất cả timesheets cho ngày này
        $timesheets = Timesheet::where('user_id', $user->id)
            ->whereDate('date', $dateKey)
            ->with(['shift', 'shiftRuleHistory', 'companyAddress', 'checkoutCompanyAddress'])
            ->orderBy('checkin')
            ->get();

        // Lấy giải trình cho ngày này với relationship
        $explanations = AttendanceExplanation::where('user_id', $user->id)
            ->whereDate('date', $dateKey)
            ->with(['remoteShift', 'managerApprover', 'hrApprover', 'taggedUser', 'shiftChange'])
            ->get();

        // Lấy tổng số giờ OT đã được duyệt cho ngày này
        $approvedOtHours = AttendanceExplanation::where('user_id', $user->id)
            ->whereDate('date', $dateKey)
            ->where('explanation_type', 'overtime')
            ->where('manager_status', 'approved')
            ->where('hr_status', 'approved')
            ->whereNotNull('ot_hours')
            ->sum('ot_hours');

        if ($timesheets->isNotEmpty()) {
            // Có dữ liệu timesheet - sử dụng logic mới với nhiều checkin/checkout
            $attendanceData = $this->dataProcessor->calculateMultipleTimesheetsStatus($timesheets, $explanations);

            // === SỬ DỤNG ATTENDANCEVIOLATIONCHECKER ĐỂ KIỂM TRA VI PHẠM ===
            $violationChecker = app(\App\Services\AttendanceExplanation\AttendanceViolationChecker::class);
            $violationResult = $violationChecker->checkViolations($attendanceData, $explanations);

            // Merge violation results vào attendance data
            $attendanceData = array_merge($attendanceData, [
                'has_violations' => $violationResult['has_violations'],
                'late_minutes' => $violationResult['late_minutes'],
                'early_minutes' => $violationResult['early_minutes'],
                'missing_checkin' => $violationResult['missing_checkin'],
                'missing_checkout' => $violationResult['missing_checkout'],
                'violations_summary' => $violationResult['violations_summary'],
                'status' => $violationResult['status'],
                'status_text' => $violationResult['status_text'],
                'status_color' => $violationResult['status_color'],
                'final_status' => $violationResult['final_status']
            ]);

            // Kiểm tra shift mismatch
            $shiftMismatch = $this->shiftMismatchService->checkShiftMismatch($timesheets, $explanations);
            $attendanceData['shift_mismatch'] = $shiftMismatch;
        } else {
            // Không có timesheet
            $finalOtHours = $this->workdayCalculator->getFinalOtHours($explanations);

            // Kiểm tra xem có phải ngày làm việc không (thứ 2-7)
            $dayOfWeek = $date->dayOfWeek; // 0=Sunday, 1=Monday, ..., 6=Saturday
            $isWorkday = $dayOfWeek >= 1 && $dayOfWeek <= 6; // Thứ 2 đến thứ 7

            $attendanceData = [
                'date' => $dateKey,
                'checkin' => null,
                'checkout' => null,
                'shift_name' => null,
                'schedule_start' => null,
                'schedule_end' => null,
                'original_workday' => 0,
                'workday' => 0,
                'work_hours' => 0,
                'real_hours' => 0,
                'is_full_workday' => false, // Không có dữ liệu = không làm đủ công
                // Thêm các field vi phạm cho consistency
                'has_violations' => true, // Không có dữ liệu = vi phạm
                'late_minutes' => 0,
                'early_minutes' => 0,
                'missing_checkin' => true,
                'missing_checkout' => true,
                'violations_summary' => [
                    ['type' => 'missing_checkin', 'message' => 'Thiếu checkin', 'severity' => 'high'],
                    ['type' => 'missing_checkout', 'message' => 'Thiếu checkout', 'severity' => 'high']
                ],
                'status' => 'no_data',
                'status_text' => $isWorkday ? 'Nghỉ làm' : 'Chưa chấm công',
                'status_color' => $isWorkday ? 'danger' : 'default',
                'final_status' => [
                    'status' => 'no_data',
                    'color' => $isWorkday ? 'danger' : 'default',
                    'text' => $isWorkday ? 'Nghỉ làm' : 'Chưa chấm công'
                ],
                'approved_ot_hours' => $finalOtHours,
                'is_adjusted' => $finalOtHours > 0,
                'adjustment_reason' => $this->dataProcessor->getAdjustmentReason($explanations),
            ];
        }

        return $this->addExplanationPermissions($attendanceData, $explanations, $user);
    }

    /**
     * Thêm thông tin permissions cho explanations
     */
    protected function addExplanationPermissions(array $attendanceData, Collection $explanations, User $user): array
    {
        // Thêm thông tin giải trình với can_update và tagged user info
        $explanationsWithPermissions = $explanations->map(function($explanation) use ($user) {
            $canUpdate = $this->canUpdateExplanation($user, $explanation);
            $explanation->can_update = $canUpdate['allowed'];

            // Thêm thông tin tagged user
            if ($explanation->taggedUser) {
                $explanation->tagged_user_name = $explanation->taggedUser->name;
            }

            return $explanation;
        });

        $attendanceData['explanations'] = $explanationsWithPermissions;
        $attendanceData['has_explanation'] = $explanations->count() > 0;
        $attendanceData['explanation_count'] = $explanations->count();

        // Cập nhật status color dựa trên trạng thái điều chỉnh và shift mismatch
        // Ưu tiên: 1. Đã điều chỉnh, 2. Có vấn đề (has_violations), 3. Shift mismatch, 4. Giải trình khác
        if ($attendanceData['is_adjusted']) {
            $attendanceData['status_color'] = 'success';
            $attendanceData['status_text'] = 'Đã điều chỉnh';
        } elseif (isset($attendanceData['has_violations']) && $attendanceData['has_violations']) {
            // Sử dụng final status từ violation checker nếu có vi phạm
            if (isset($attendanceData['final_status'])) {
                $attendanceData['status'] = $attendanceData['final_status']['status'];
                $attendanceData['status_color'] = $attendanceData['final_status']['color'];
                $attendanceData['status_text'] = $attendanceData['final_status']['text'];
            }
        } elseif (isset($attendanceData['shift_mismatch']) && $attendanceData['shift_mismatch'] && $attendanceData['shift_mismatch']['has_mismatch']) {
            // Chỉ hiển thị shift mismatch khi không có vấn đề nghiêm trọng khác
            $attendanceData['status'] = 'shift_mismatch';
            $attendanceData['status_color'] = 'warning';
            $attendanceData['status_text'] = 'Lựa chọn sai ca làm việc';
        } elseif ($explanations->count() > 0) {
            $attendanceData['status_color'] = 'info';
            $attendanceData['status_text'] = 'Đã giải trình';
        } else {
            // Không có vi phạm và không có giải trình
            $attendanceData['status'] = 'perfect';
            $attendanceData['status_color'] = 'success';
            $attendanceData['status_text'] = 'Hoàn hảo';
        }

        return $attendanceData;
    }

    /**
     * Kiểm tra có thể cập nhật giải trình không (chưa có ai duyệt)
     */
    public function canUpdateExplanation(User $user, AttendanceExplanation $explanation): array
    {
        // Chỉ người tạo mới có thể update
        if ($explanation->user_id !== $user->id) {
            return [
                'allowed' => false,
                'message' => 'Bạn không có quyền cập nhật giải trình này'
            ];
        }

        // Kiểm tra tagged user đã confirm chưa
        if ($explanation->tagged_user_id && $explanation->tagged_user_status !== 'pending') {
            return [
                'allowed' => false,
                'message' => 'Không thể cập nhật vì user được tag đã xử lý giải trình này'
            ];
        }

        // Kiểm tra manager đã duyệt chưa
        if ($explanation->manager_status !== 'pending') {
            return [
                'allowed' => false,
                'message' => 'Không thể cập nhật vì manager đã xử lý giải trình này'
            ];
        }

        // Kiểm tra HR đã duyệt chưa
        if ($explanation->hr_status !== 'pending') {
            return [
                'allowed' => false,
                'message' => 'Không thể cập nhật vì HR đã xử lý giải trình này'
            ];
        }

        // Chỉ cho phép update khi trạng thái là pending_tagged_user, pending, hoặc paused
        $allowedStatuses = ['pending_tagged_user', 'pending', 'paused'];
        if (!in_array($explanation->final_status, $allowedStatuses)) {
            return [
                'allowed' => false,
                'message' => 'Không thể cập nhật giải trình ở trạng thái hiện tại'
            ];
        }

        return [
            'allowed' => true,
            'message' => 'Có thể cập nhật giải trình'
        ];
    }
}
