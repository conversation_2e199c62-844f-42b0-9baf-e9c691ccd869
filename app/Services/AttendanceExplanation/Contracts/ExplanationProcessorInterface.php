<?php

namespace App\Services\AttendanceExplanation\Contracts;

use Illuminate\Support\Collection;

interface ExplanationProcessorInterface
{
    /**
     * Xử lý dữ liệu chấm công cho explanation-only row (không có timesheet)
     *
     * @param array $baseData
     * @param object $explanation
     * @return array
     */
    public function processExplanationOnlyRow(array $baseData, object $explanation): array;

    /**
     * Áp dụng explanation vào dữ liệu chấm công có timesheet
     *
     * @param array $attendanceData
     * @param Collection $explanations
     * @param object|null $timesheet
     * @return array
     */
    public function applyToAttendanceData(array $attendanceData, Collection $explanations, ?object $timesheet = null): array;

    /**
     * Tính toán workday cho explanation type này
     *
     * @param object|null $timesheet
     * @param object $explanation
     * @return float
     */
    public function calculateWorkday(?object $timesheet, object $explanation): float;

    /**
     * Xác đ<PERSON>nh attendance status cho explanation type này
     *
     * @param object|null $timesheet
     * @param object $explanation
     * @return string
     */
    public function determineAttendanceStatus(?object $timesheet, object $explanation): string;

    /**
     * Kiểm tra explanation type này có ảnh hưởng đến penalty không
     *
     * @return bool
     */
    public function affectsPenalty(): bool;

    /**
     * Lấy explanation summary text
     *
     * @param Collection $explanations
     * @return string
     */
    public function getExplanationSummary(Collection $explanations): string;
}
