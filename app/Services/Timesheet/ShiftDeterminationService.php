<?php

namespace App\Services\Timesheet;

use App\Models\Timesheet;
use App\Models\Shift;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class ShiftDeterminationService
{
    /**
     * Xác định ca làm việc để tính công dựa trên ưu tiên:
     * 1. Ca checkout (nếu có)
     * 2. Ca checkin (nếu không có checkout)
     * 
     * @param Timesheet|Collection $timesheets
     * @return Shift|null
     */
    public function determineWorkdayShift($timesheets): ?Shift
    {
        // Nếu là single timesheet
        if ($timesheets instanceof Timesheet) {
            return $this->determineShiftForSingleTimesheet($timesheets);
        }

        // Nếu là collection of timesheets (multiple checkin/checkout)
        if ($timesheets instanceof Collection) {
            return $this->determineShiftForMultipleTimesheets($timesheets);
        }

        return null;
    }

    /**
     * Xác định ca cho single timesheet
     */
    private function determineShiftForSingleTimesheet(Timesheet $timesheet): ?Shift
    {
        // Nếu có checkout, ưu tiên sử dụng ca checkout
        if ($timesheet->checkout) {
            return $this->findBestShiftForTime($timesheet->checkout, $timesheet->user->shifts);
        }

        // Nếu chỉ có checkin, sử dụng ca checkin
        if ($timesheet->checkin) {
            return $this->findBestShiftForTime($timesheet->checkin, $timesheet->user->shifts);
        }

        // Fallback về ca hiện tại trong timesheet
        return $timesheet->shift;
    }

    /**
     * Xác định ca cho multiple timesheets
     */
    private function determineShiftForMultipleTimesheets(Collection $timesheets): ?Shift
    {
        // Lấy checkout cuối cùng
        $latestCheckout = $timesheets->filter(function($timesheet) {
            return $timesheet->checkout !== null;
        })->sortByDesc('checkout')->first();

        if ($latestCheckout) {
            $userShifts = $latestCheckout->user->shifts;
            return $this->findBestShiftForTime($latestCheckout->checkout, $userShifts);
        }

        // Nếu không có checkout, lấy checkin đầu tiên
        $earliestCheckin = $timesheets->filter(function($timesheet) {
            return $timesheet->checkin !== null;
        })->sortBy('checkin')->first();

        if ($earliestCheckin) {
            $userShifts = $earliestCheckin->user->shifts;
            return $this->findBestShiftForTime($earliestCheckin->checkin, $userShifts);
        }

        // Fallback về ca của timesheet đầu tiên
        return $timesheets->first()?->shift;
    }

    /**
     * Tìm ca phù hợp nhất cho thời gian cụ thể
     */
    private function findBestShiftForTime(Carbon $time, Collection $userShifts): ?Shift
    {
        $timeOnly = $time->format('H:i:s');
        $bestShift = null;
        $minDistance = PHP_INT_MAX;

        foreach ($userShifts as $shift) {
            // Lấy shift rule cho ngày này
            $shiftRule = $shift->getActiveRule($time->format('Y-m-d'));
            
            $shiftStart = $shiftRule ? $shiftRule->start_time : $shift->start_time;
            $shiftEnd = $shiftRule ? $shiftRule->end_time : $shift->end_time;

            // Tính khoảng cách từ thời gian đến ca làm việc
            $distance = $this->calculateTimeDistance($timeOnly, $shiftStart, $shiftEnd);

            if ($distance < $minDistance) {
                $minDistance = $distance;
                $bestShift = $shift;
            }
        }

        return $bestShift;
    }

    /**
     * Tính khoảng cách từ thời gian đến ca làm việc
     */
    private function calculateTimeDistance(string $time, string $shiftStart, string $shiftEnd): int
    {
        $timeMinutes = $this->timeToMinutes($time);
        $startMinutes = $this->timeToMinutes($shiftStart);
        $endMinutes = $this->timeToMinutes($shiftEnd);

        // Nếu thời gian nằm trong ca làm việc, khoảng cách = 0
        if ($timeMinutes >= $startMinutes && $timeMinutes <= $endMinutes) {
            return 0;
        }

        // Tính khoảng cách đến điểm gần nhất của ca
        $distanceToStart = abs($timeMinutes - $startMinutes);
        $distanceToEnd = abs($timeMinutes - $endMinutes);

        return min($distanceToStart, $distanceToEnd);
    }

    /**
     * Chuyển đổi thời gian thành phút
     */
    private function timeToMinutes(string $time): int
    {
        $parts = explode(':', $time);
        return intval($parts[0]) * 60 + intval($parts[1]);
    }

    /**
     * Lấy shift rule phù hợp cho timesheet với ca đã xác định
     */
    public function getShiftRuleForWorkday(Timesheet $timesheet, ?Shift $workdayShift = null): ?\App\Models\ShiftRuleHistory
    {
        $shift = $workdayShift ?? $this->determineWorkdayShift($timesheet);
        
        if (!$shift) {
            return null;
        }

        return $shift->getActiveRule($timesheet->date);
    }

    /**
     * Tính workday với ca đã xác định
     */
    public function calculateWorkdayWithDeterminedShift(Timesheet $timesheet): float
    {
        $workdayShift = $this->determineWorkdayShift($timesheet);
        
        if (!$workdayShift || !$timesheet->checkin || !$timesheet->checkout) {
            return 0;
        }

        $shiftRule = $this->getShiftRuleForWorkday($timesheet, $workdayShift);
        $shiftLogicService = app(\App\Services\Timesheet\ShiftLogicType\ShiftLogicType::class);

        if (!$shiftRule) {
            // Fallback về shift gốc
            return $shiftLogicService->calcWorkday([
                'schedule_start_time' => $workdayShift->start_time,
                'schedule_end_time' => $workdayShift->end_time,
                'start_time' => $timesheet->checkin->format('H:i:s'),
                'end_time' => $timesheet->checkout->format('H:i:s'),
                'workday_min_1' => $workdayShift->workday_min_1,
                'workday_min_2' => $workdayShift->workday_min_2,
                'default_workday_threshold' => $workdayShift->default_workday_threshold ?? 1.0,
            ]);
        }

        return $shiftLogicService->calcWorkday([
            'schedule_start_time' => $shiftRule->start_time,
            'schedule_end_time' => $shiftRule->end_time,
            'start_time' => $timesheet->checkin->format('H:i:s'),
            'end_time' => $timesheet->checkout->format('H:i:s'),
            'workday_min_1' => $shiftRule->workday_min_1,
            'workday_min_2' => $shiftRule->workday_min_2,
            'break_times' => $shiftRule->break_times,
            'default_workday_threshold' => $workdayShift->default_workday_threshold ?? 1.0,
        ]);
    }
}
