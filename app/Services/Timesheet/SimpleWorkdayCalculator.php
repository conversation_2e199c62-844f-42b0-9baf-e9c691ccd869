<?php

namespace App\Services\Timesheet;

use App\Models\Timesheet;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class SimpleWorkdayCalculator
{
    /**
     * Tính workday đơn giản cho multiple timesheets:
     * - Checkin đầu tiên → Checkout cuối cùng
     * - Nếu không có checkout cuối → dùng checkin cuối làm checkout
     * 
     * @param Collection $timesheets
     * @return array
     */
    public function calculateForMultipleTimesheets(Collection $timesheets): array
    {
        if ($timesheets->isEmpty()) {
            return [
                'smart_checkin' => null,
                'smart_checkout' => null,
                'workday_hours' => 0,
                'workday' => 0,
                'method' => 'empty'
            ];
        }

        // Nếu chỉ có 1 timesheet, xử lý đơn giản
        if ($timesheets->count() === 1) {
            $timesheet = $timesheets->first();
            return [
                'smart_checkin' => $timesheet->checkin,
                'smart_checkout' => $timesheet->checkout,
                'workday_hours' => $timesheet->calcWorkDayHours(),
                'workday' => $timesheet->calcWorkday(),
                'method' => 'single'
            ];
        }

        // Multiple timesheets - áp dụng logic đơn giản
        $smartCheckin = $this->getEarliestCheckin($timesheets);
        $smartCheckout = $this->getSmartCheckout($timesheets);

        if (!$smartCheckin || !$smartCheckout) {
            return [
                'smart_checkin' => $smartCheckin,
                'smart_checkout' => $smartCheckout,
                'workday_hours' => 0,
                'workday' => 0,
                'method' => 'incomplete'
            ];
        }

        // Tính workday dựa trên smart checkin/checkout
        $workdayData = $this->calculateWorkdayFromTimes(
            $smartCheckin, 
            $smartCheckout, 
            $timesheets->first() // Dùng timesheet đầu tiên để lấy shift info
        );

        return [
            'smart_checkin' => $smartCheckin,
            'smart_checkout' => $smartCheckout,
            'workday_hours' => $workdayData['hours'],
            'workday' => $workdayData['workday'],
            'method' => 'multiple_smart'
        ];
    }

    /**
     * Lấy checkin sớm nhất
     */
    private function getEarliestCheckin(Collection $timesheets): ?Carbon
    {
        return $timesheets
            ->filter(fn($t) => $t->checkin !== null)
            ->min('checkin');
    }

    /**
     * Lấy "smart checkout":
     * So sánh thời gian muộn nhất giữa checkout cuối và checkin cuối
     * Lấy thời gian nào muộn hơn
     */
    private function getSmartCheckout(Collection $timesheets): ?Carbon
    {
        // Tìm checkout cuối cùng
        $latestCheckout = $timesheets
            ->filter(fn($t) => $t->checkout !== null)
            ->sortByDesc('checkout')
            ->first()?->checkout;

        // Tìm checkin cuối cùng
        $latestCheckin = $timesheets
            ->filter(fn($t) => $t->checkin !== null)
            ->sortByDesc('checkin')
            ->first()?->checkin;

        // So sánh và lấy thời gian muộn nhất
        if ($latestCheckout && $latestCheckin) {
            return $latestCheckout->gt($latestCheckin) ? $latestCheckout : $latestCheckin;
        }

        // Nếu chỉ có một trong hai
        return $latestCheckout ?: $latestCheckin;
    }

    /**
     * Tính workday từ smart checkin/checkout
     */
    private function calculateWorkdayFromTimes(Carbon $checkin, Carbon $checkout, $referenceTimesheet): array
    {
        $shiftLogicService = app(\App\Services\Timesheet\ShiftLogicType\ShiftLogicType::class);
        
        // Lấy shift rule từ timesheet tham chiếu
        $shiftRule = null;
        if (method_exists($referenceTimesheet, 'getShiftRule')) {
            $shiftRule = $referenceTimesheet->getShiftRule();
        } elseif (is_callable($referenceTimesheet->getShiftRule ?? null)) {
            $shiftRule = call_user_func($referenceTimesheet->getShiftRule);
        }

        $shift = $referenceTimesheet->shift;

        $data = [
            'start_time' => $checkin->format('H:i:s'),
            'end_time' => $checkout->format('H:i:s'),
        ];

        if ($shiftRule) {
            $data = array_merge($data, [
                'schedule_start_time' => $shiftRule->start_time,
                'schedule_end_time' => $shiftRule->end_time,
                'workday_min_1' => $shiftRule->workday_min_1,
                'workday_min_2' => $shiftRule->workday_min_2,
                'break_times' => $shiftRule->break_times,
                'default_workday_threshold' => $shift->default_workday_threshold ?? 1.0,
            ]);
        } else {
            $data = array_merge($data, [
                'schedule_start_time' => $shift->start_time,
                'schedule_end_time' => $shift->end_time,
                'workday_min_1' => $shift->workday_min_1,
                'workday_min_2' => $shift->workday_min_2,
                'default_workday_threshold' => $shift->default_workday_threshold ?? 1.0,
            ]);
        }

        $hours = $shiftLogicService->calcWorkdayHours($data);
        $workday = $shiftLogicService->calcWorkday($data);

        return [
            'hours' => $hours,
            'workday' => $workday
        ];
    }

    /**
     * Tính workday cho single timesheet với option sử dụng smart logic
     */
    public function calculateForSingleTimesheet(Timesheet $timesheet, bool $useSmartLogic = false): array
    {
        if ($useSmartLogic) {
            // Có thể mở rộng logic smart cho single timesheet ở đây
            // Ví dụ: xác định ca dựa trên checkout time
        }

        return [
            'smart_checkin' => $timesheet->checkin,
            'smart_checkout' => $timesheet->checkout,
            'workday_hours' => $timesheet->calcWorkDayHours(),
            'workday' => $timesheet->calcWorkday(),
            'method' => 'single_standard'
        ];
    }

    /**
     * Method chính để tính workday cho bất kỳ trường hợp nào
     */
    public function calculate($timesheets, bool $useSmartLogic = false): array
    {
        if ($timesheets instanceof Timesheet) {
            return $this->calculateForSingleTimesheet($timesheets, $useSmartLogic);
        }

        if ($timesheets instanceof Collection) {
            return $this->calculateForMultipleTimesheets($timesheets);
        }

        return [
            'smart_checkin' => null,
            'smart_checkout' => null,
            'workday_hours' => 0,
            'workday' => 0,
            'method' => 'invalid_input'
        ];
    }

    /**
     * Tạo summary text cho debug
     */
    public function getSummary($timesheets): string
    {
        $result = $this->calculate($timesheets);
        
        $summary = "Method: {$result['method']}\n";
        $summary .= "Smart Checkin: " . ($result['smart_checkin']?->format('H:i:s') ?? 'N/A') . "\n";
        $summary .= "Smart Checkout: " . ($result['smart_checkout']?->format('H:i:s') ?? 'N/A') . "\n";
        $summary .= "Workday Hours: " . number_format($result['workday_hours'], 2) . "\n";
        $summary .= "Workday: " . number_format($result['workday'], 2) . "\n";

        if ($timesheets instanceof Collection && $timesheets->count() > 1) {
            $summary .= "\nOriginal timesheets:\n";
            foreach ($timesheets as $i => $timesheet) {
                $summary .= "  " . ($i + 1) . ". " . 
                    ($timesheet->checkin?->format('H:i:s') ?? 'N/A') . " → " . 
                    ($timesheet->checkout?->format('H:i:s') ?? 'N/A') . "\n";
            }
        }

        return $summary;
    }
}
