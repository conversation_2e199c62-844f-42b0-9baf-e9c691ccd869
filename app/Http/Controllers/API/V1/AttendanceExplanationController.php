<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Models\AttendanceExplanation;
use App\Models\User;
use App\Notifications\AttendanceExplanationTaggedNotification;
use App\Services\AttendanceExplanation\AttendanceExplanationService;
use Illuminate\Http\Request;
use Carbon\Carbon;

class AttendanceExplanationController extends Controller
{
    protected AttendanceExplanationService $attendanceExplanationService;

    public function __construct(AttendanceExplanationService $attendanceExplanationService)
    {
        $this->attendanceExplanationService = $attendanceExplanationService;
    }

    /**
     * Lấy calendar chấm công theo tháng
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMonthlyCalendar(Request $request)
    {
        try {
            $user = auth()->user();

            // Validate input
            $request->validate([
                'month' => 'nullable|integer|min:1|max:12',
                'year' => 'nullable|integer|min:2020|max:2030'
            ]);

            // Lấy tháng/năm từ request hoặc mặc định là tháng hiện tại
            $month = $request->get('month', now()->month);
            $year = $request->get('year', now()->year);

            // Validate tháng/năm
            $month = max(1, min(12, (int)$month));
            $year = max(2020, min(2030, (int)$year));

            $currentDate = Carbon::create($year, $month, 1);

            // Sử dụng chính xác logic từ attendance-explanation page
            $attendanceData = $this->attendanceExplanationService->getMonthlyAttendanceForCalendar($user, $currentDate);

            // Tạo calendar data cho API (giữ format cũ)
            $calendarData = $this->attendanceExplanationService->generateCalendarDataForApi($currentDate, $attendanceData);

            // Lấy manager mặc định
            $defaultManager = $this->getDefaultManagerInfo($user);

            // Lấy danh sách đồng nghiệp
            $colleagues = $this->getColleagues($user);

            // Lấy thống kê giải trình trong tháng
            $monthlyStats = $this->attendanceExplanationService->getMonthlyExplanationStats($user, $currentDate);

            return response()->json([
                'success' => true,
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'account' => $user->account,
                    ],
                    'current_date' => $currentDate->tz('Asia/Ho_Chi_Minh')->startOfDay()->format('Y-m-d\TH:i:s.uP'),
                    'calendar_data' => $calendarData,
                    'month' => $month,
                    'year' => $year,
                    'prev_month' => $currentDate->copy()->subMonth()->tz('Asia/Ho_Chi_Minh')->startOfDay()->format('Y-m-d\TH:i:s.uP'),
                    'next_month' => $currentDate->copy()->addMonth()->tz('Asia/Ho_Chi_Minh')->startOfDay()->format('Y-m-d\TH:i:s.uP'),
                    'default_manager' => $defaultManager,
                    'colleagues' => $colleagues,
                    'monthly_stats' => $monthlyStats,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lấy thông tin chấm công cho một ngày cụ thể
     * 
     * @param Request $request
     * @param string $date
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDayAttendance(Request $request, string $date)
    {
        try {
            $user = auth()->user();
            $targetDate = Carbon::parse($date);

            // Lấy thông tin chấm công cho ngày
            $attendanceInfo = $this->attendanceExplanationService->getDayAttendance($user, $targetDate);

            if (!$attendanceInfo) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy dữ liệu chấm công cho ngày này'
                ], 404);
            }

            // Lấy danh sách giải trình cho ngày này
            $explanations = AttendanceExplanation::where('user_id', $user->id)
                ->where('date', $date)
                ->with(['remoteShift', 'managerApprover', 'hrApprover', 'taggedUser'])
                ->get();

            // Format explanations data với ISO 8601 timestamps (Asia/Ho_Chi_Minh timezone)
            $formattedExplanations = $explanations->map(function ($explanation) {
                return [
                    'id' => $explanation->id,
                    'date' => $explanation->date->tz('Asia/Ho_Chi_Minh')->startOfDay()->format('Y-m-d\TH:i:s.uP'),
                    'explanation' => $explanation->explanation,
                    'explanation_type' => $explanation->explanation_type,
                    'explanation_type_text' => $explanation->explanation_type_text,
                    'ot_hours' => $explanation->ot_hours,
                    'remote_shift' => $explanation->remoteShift ? [
                        'id' => $explanation->remoteShift->id,
                        'name' => $explanation->remoteShift->name,
                        'start_time' => $explanation->remoteShift->start_time,
                        'end_time' => $explanation->remoteShift->end_time,
                    ] : null,
                    'final_status' => $explanation->final_status,
                    'manager_status' => $explanation->manager_status,
                    'hr_status' => $explanation->hr_status,
                    'manager_approved_at' => $explanation->manager_approved_at ? $explanation->manager_approved_at->tz('Asia/Ho_Chi_Minh')->format('Y-m-d\TH:i:s.uP') : null,
                    'hr_approved_at' => $explanation->hr_approved_at ? $explanation->hr_approved_at->tz('Asia/Ho_Chi_Minh')->format('Y-m-d\TH:i:s.uP') : null,
                    'manager_note' => $explanation->manager_note,
                    'hr_note' => $explanation->hr_note,
                    'tagged_user_id' => $explanation->tagged_user_id,
                    'tagged_user_name' => $explanation->taggedUser ? $explanation->taggedUser->name : null,
                    'tagged_user_status' => $explanation->tagged_user_status,
                    'tagged_user_confirmed_at' => $explanation->tagged_user_confirmed_at ? $explanation->tagged_user_confirmed_at->tz('Asia/Ho_Chi_Minh')->format('Y-m-d\TH:i:s.uP') : null,
                    'tagged_user_note' => $explanation->tagged_user_note,
                    'created_at' => $explanation->created_at->tz('Asia/Ho_Chi_Minh')->format('Y-m-d\TH:i:s.uP'),
                    'can_update' => $this->attendanceExplanationService->canUpdateExplanation($user, $explanation)['allowed'] ?? false,
                ];
            });

            // Format attendance info với ISO 8601 timestamps
            $formattedAttendanceInfo = null;
            if ($attendanceInfo) {
                $formattedAttendanceInfo = $this->attendanceExplanationService->formatAttendanceDataForApi($attendanceInfo);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'attendance' => $formattedAttendanceInfo,
                    'explanations' => $formattedExplanations
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in getDayAttendance: ' . $e->getMessage(), [
                'date' => $date,
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tải dữ liệu: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Tạo giải trình chấm công mới
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $user = auth()->user();

            // Lấy danh sách shift IDs mà user được phép sử dụng
            $availableShiftIds = $user->getAvailableShifts()->pluck('id')->toArray();

            $request->validate([
                'date' => 'required|date',
                'explanation' => 'required|string|max:500',
                'explanation_type' => 'required|in:late,early,insufficient_hours,no_checkin,no_checkout,overtime,remote_work,shift_change,new_employee_no_account,other',
                'ot_hours' => 'nullable|numeric|min:0.1|max:24|required_if:explanation_type,overtime',
                'remote_shift_id' => [
                    'nullable',
                    'required_if:explanation_type,remote_work',
                    function ($attribute, $value, $fail) use ($availableShiftIds) {
                        if ($value && !in_array($value, $availableShiftIds)) {
                            $fail('Ca làm việc được chọn không hợp lệ hoặc bạn không có quyền sử dụng.');
                        }
                    }
                ],
                'shift_change_id' => [
                    'nullable',
                    'required_if:explanation_type,shift_change',
                    function ($attribute, $value, $fail) use ($availableShiftIds) {
                        if ($value && !in_array($value, $availableShiftIds)) {
                            $fail('Ca làm việc được chọn không hợp lệ hoặc bạn không có quyền sử dụng.');
                        }
                    }
                ],
                'tagged_user_id' => [
                    'nullable',
                    'exists:users,id',
                    function ($attribute, $value, $fail) {
                        if ($value) {
                            $user = User::find($value);
                            if (!$user || !$user->active_flag || $user->deleted_at) {
                                $fail('User được chọn không hợp lệ hoặc không còn hoạt động.');
                            }
                        }
                    }
                ],
            ]);

            $date = Carbon::parse($request->date);

            // Kiểm tra giới hạn 3 giải trình/tháng (trừ OT, remote work, new_employee_no_account và other)
            if (!in_array($request->explanation_type, ['overtime', 'remote_work', 'new_employee_no_account', 'other'])) {
                $canCreate = $this->attendanceExplanationService->canCreateExplanation($user, $date);
                if (!$canCreate['allowed']) {
                    return response()->json([
                        'success' => false,
                        'message' => $canCreate['message']
                    ], 400);
                }
            }

            // Xác định trạng thái ban đầu dựa trên có tagged user hay không
            $initialFinalStatus = $request->tagged_user_id ? 'pending_tagged_user' : 'pending';

            // Tạo giải trình
            $explanationData = [
                'user_id' => $user->id,
                'date' => $date->format('Y-m-d'),
                'explanation' => $request->explanation,
                'explanation_type' => $request->explanation_type,
                'ot_hours' => $request->ot_hours,
                'remote_shift_id' => $request->remote_shift_id,
                'created_by' => $user->id,
                'manager_status' => 'pending',
                'hr_status' => 'pending',
                'final_status' => $initialFinalStatus,
            ];

            // Chỉ set tagged_user_id và tagged_user_status khi có tagged user
            if ($request->tagged_user_id) {
                $explanationData['tagged_user_id'] = $request->tagged_user_id;
                $explanationData['tagged_user_status'] = 'pending';
            } else {
                // Đảm bảo tagged_user_status là NULL khi không có tagged user
                $explanationData['tagged_user_status'] = null;
            }

            $explanation = AttendanceExplanation::create($explanationData);

            if ($explanation) {
                // Gửi notification cho user được tag (nếu có)
                if ($request->tagged_user_id) {
                    $taggedUser = User::find($request->tagged_user_id);
                    if ($taggedUser) {
                        $taggedUser->notify(new AttendanceExplanationTaggedNotification($explanation, $user));
                    }
                }

                return response()->json([
                    'success' => true,
                    'message' => 'Đã lưu giải trình thành công',
                    'data' => [
                        'id' => $explanation->id,
                        'date' => $explanation->date->tz('Asia/Ho_Chi_Minh')->startOfDay()->format('Y-m-d\TH:i:s.uP'),
                        'explanation_type' => $explanation->explanation_type,
                        'final_status' => $explanation->final_status,
                    ]
                ], 201);
            }

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi lưu giải trình'
            ], 500);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lấy thông tin manager mặc định
     */
    protected function getDefaultManagerInfo($user)
    {
        $manager = \DB::table('user_managers')
            ->join('users', 'users.id', '=', 'user_managers.manager_user_id')
            ->where('user_managers.user_id', $user->id)
            ->whereNull('user_managers.deleted_at')
            ->whereNull('users.deleted_at')
            ->where('users.active_flag', true)
            ->select('users.id', 'users.name', 'users.phone_number')
            ->first();

        return $manager;
    }

    /**
     * Lấy danh sách đồng nghiệp
     */
    protected function getColleagues($user)
    {
        // Tìm manager của user hiện tại
        $managerInfo = $this->getDefaultManagerInfo($user);

        if (!$managerInfo) {
            return collect(); // Không có manager thì không có đồng nghiệp
        }

        // Lấy tất cả users được quản lý bởi manager này (trừ chính user hiện tại)
        $colleagueIds = \DB::table('user_managers')
            ->where('manager_user_id', $managerInfo->id)
            ->where('user_id', '!=', $user->id) // Loại trừ chính user hiện tại
            ->whereNull('deleted_at')
            ->pluck('user_id');

        return \App\Models\User::whereIn('id', $colleagueIds)
            ->where('active_flag', true)
            ->whereNull('deleted_at')
            ->with(['staffDepartment'])
            ->select('id', 'name', 'phone_number', 'account', 'staff_department_id')
            ->orderBy('name')
            ->get()
            ->map(function ($colleague) {
                return [
                    'id' => $colleague->id,
                    'name' => $colleague->name,
                    'account' => $colleague->account,
                    'phone_number' => $colleague->phone_number ? substr($colleague->phone_number, -4) : null,
                    'department_name' => $colleague->staffDepartment ? $colleague->staffDepartment->name : null,
                ];
            });
    }

    /**
     * Lấy thông tin chi tiết một giải trình để edit
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $user = auth()->user();
            $explanation = AttendanceExplanation::where('id', $id)
                ->where('user_id', $user->id)
                ->with(['remoteShift', 'taggedUser'])
                ->first();

            if (!$explanation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy giải trình hoặc bạn không có quyền truy cập'
                ], 404);
            }

            // Kiểm tra quyền sửa
            $canUpdate = $this->attendanceExplanationService->canUpdateExplanation($user, $explanation);
            if (!$canUpdate['allowed']) {
                return response()->json([
                    'success' => false,
                    'message' => $canUpdate['message']
                ], 403);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $explanation->id,
                    'date' => $explanation->date->tz('Asia/Ho_Chi_Minh')->startOfDay()->format('Y-m-d\TH:i:s.uP'),
                    'explanation' => $explanation->explanation,
                    'explanation_type' => $explanation->explanation_type,
                    'ot_hours' => $explanation->ot_hours,
                    'remote_shift_id' => $explanation->remote_shift_id,
                    'tagged_user_id' => $explanation->tagged_user_id,
                    'remote_shift' => $explanation->remoteShift ? [
                        'id' => $explanation->remoteShift->id,
                        'name' => $explanation->remoteShift->name,
                    ] : null,
                    'tagged_user' => $explanation->taggedUser ? [
                        'id' => $explanation->taggedUser->id,
                        'name' => $explanation->taggedUser->name,
                    ] : null,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cập nhật giải trình
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            $user = auth()->user();
            $explanation = AttendanceExplanation::where('id', $id)
                ->where('user_id', $user->id)
                ->first();

            if (!$explanation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy giải trình hoặc bạn không có quyền truy cập'
                ], 404);
            }

            // Kiểm tra quyền sửa
            $canUpdate = $this->attendanceExplanationService->canUpdateExplanation($user, $explanation);
            if (!$canUpdate['allowed']) {
                return response()->json([
                    'success' => false,
                    'message' => $canUpdate['message']
                ], 403);
            }

            // Lấy danh sách shift IDs mà user được phép sử dụng
            $availableShiftIds = $user->getAvailableShifts()->pluck('id')->toArray();

            $request->validate([
                'explanation' => 'required|string|max:500',
                'explanation_type' => 'required|in:late,early,insufficient_hours,no_checkin,no_checkout,overtime,remote_work,shift_change,new_employee_no_account,other',
                'ot_hours' => 'nullable|numeric|min:0.1|max:24|required_if:explanation_type,overtime',
                'remote_shift_id' => [
                    'nullable',
                    'required_if:explanation_type,remote_work',
                    function ($attribute, $value, $fail) use ($availableShiftIds) {
                        if ($value && !in_array($value, $availableShiftIds)) {
                            $fail('Ca làm việc được chọn không hợp lệ hoặc bạn không có quyền sử dụng.');
                        }
                    }
                ],
                'shift_change_id' => [
                    'nullable',
                    'required_if:explanation_type,shift_change',
                    function ($attribute, $value, $fail) use ($availableShiftIds) {
                        if ($value && !in_array($value, $availableShiftIds)) {
                            $fail('Ca làm việc được chọn không hợp lệ hoặc bạn không có quyền sử dụng.');
                        }
                    }
                ],
                'tagged_user_id' => [
                    'nullable',
                    'exists:users,id',
                    function ($attribute, $value, $fail) {
                        if ($value) {
                            $user = User::find($value);
                            if (!$user || !$user->active_flag || $user->deleted_at) {
                                $fail('User được chọn không hợp lệ hoặc không còn hoạt động.');
                            }
                        }
                    }
                ],
            ]);

            // Cập nhật dữ liệu
            $updateData = [
                'explanation' => $request->explanation,
                'explanation_type' => $request->explanation_type,
                'ot_hours' => $request->explanation_type === 'overtime' ? $request->ot_hours : null,
                'remote_shift_id' => $request->explanation_type === 'remote_work' ? $request->remote_shift_id : null,
            ];

            // Xử lý tagged user
            if ($request->has('tagged_user_id')) {
                $updateData['tagged_user_id'] = $request->tagged_user_id;
                if ($request->tagged_user_id) {
                    $updateData['tagged_user_status'] = 'pending';
                    // Reset final_status nếu có tagged user mới
                    $updateData['final_status'] = 'pending_tagged_user';
                } else {
                    $updateData['tagged_user_status'] = null;
                    $updateData['final_status'] = 'pending';
                }
            }

            $explanation->update($updateData);

            // Gửi notification cho user được tag mới (nếu có)
            if ($request->tagged_user_id && $request->tagged_user_id != $explanation->getOriginal('tagged_user_id')) {
                $taggedUser = User::find($request->tagged_user_id);
                if ($taggedUser) {
                    $taggedUser->notify(new AttendanceExplanationTaggedNotification($explanation, $user));
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Đã cập nhật giải trình thành công',
                'data' => [
                    'id' => $explanation->id,
                    'explanation_type' => $explanation->explanation_type,
                    'final_status' => $explanation->final_status,
                ]
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Xóa giải trình
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $user = auth()->user();
            $explanation = AttendanceExplanation::where('id', $id)
                ->where('user_id', $user->id)
                ->first();

            if (!$explanation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy giải trình hoặc bạn không có quyền truy cập'
                ], 404);
            }

            // Kiểm tra quyền xóa
            $canDelete = $this->attendanceExplanationService->canDeleteExplanation($user, $explanation);
            if (!$canDelete['allowed']) {
                return response()->json([
                    'success' => false,
                    'message' => $canDelete['message']
                ], 403);
            }

            $explanation->delete();

            return response()->json([
                'success' => true,
                'message' => 'Đã xóa giải trình thành công'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Tạm dừng giải trình
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function pause($id)
    {
        try {
            $user = auth()->user();
            $explanation = AttendanceExplanation::where('id', $id)
                ->where('user_id', $user->id)
                ->first();

            if (!$explanation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy giải trình hoặc bạn không có quyền truy cập'
                ], 404);
            }

            $result = $this->attendanceExplanationService->pauseExplanation($user, $explanation);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message']
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => $result['message']
            ], 400);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lấy danh sách giải trình của user đang login với phân trang
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMyExplanations(Request $request)
    {
        try {
            $user = auth()->user();

            // Validate input
            $request->validate([
                'page' => 'nullable|integer|min:1',
                'per_page' => 'nullable|integer|min:1|max:100',
                'status' => 'nullable|in:pending,pending_tagged_user,approved,rejected,paused',
                'explanation_type' => 'nullable|array',
                'explanation_type.*' => 'nullable|in:late,early,insufficient_hours,no_checkin,no_checkout,overtime,remote_work,shift_change,new_employee_no_account,other',
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date|after_or_equal:date_from',
                'month' => 'nullable|integer|min:1|max:12',
                'year' => 'nullable|integer|min:2020|max:2030',
                'sort_by' => 'nullable|in:date,created_at,final_status',
                'sort_order' => 'nullable|in:asc,desc'
            ]);

            // Lấy parameters
            $page = $request->get('page', 1);
            $perPage = $request->get('per_page', 15);
            $status = $request->get('status');
            $explanationTypes = $request->get('explanation_type', []); // Lấy array thay vì string
            $dateFrom = $request->get('date_from');
            $dateTo = $request->get('date_to');
            $month = $request->get('month');
            $year = $request->get('year');
            $sortBy = $request->get('sort_by', 'date');
            $sortOrder = $request->get('sort_order', 'desc');

            // Build query
            $query = AttendanceExplanation::where('user_id', $user->id)
                ->with(['remoteShift', 'managerApprover', 'hrApprover', 'taggedUser']);

            // Filter by status
            if ($status) {
                $query->where('final_status', $status);
            }

            // Filter by explanation types (array)
            if (!empty($explanationTypes)) {
                $query->whereIn('explanation_type', $explanationTypes);
            }

            // Filter by month and year
            if ($month && $year) {
                $query->whereYear('date', $year)->whereMonth('date', $month);
            } elseif ($month) {
                $query->whereMonth('date', $month);
            } elseif ($year) {
                $query->whereYear('date', $year);
            }

            // Filter by date range (chỉ áp dụng nếu không có filter month/year)
            if (!$month && !$year) {
                if ($dateFrom) {
                    $query->where('date', '>=', $dateFrom);
                }
                if ($dateTo) {
                    $query->where('date', '<=', $dateTo);
                }
            }

            // Sort
            $query->orderBy($sortBy, $sortOrder);

            // Paginate
            $explanations = $query->paginate($perPage, ['*'], 'page', $page);

            // Format data
            $formattedExplanations = $explanations->getCollection()->map(function ($explanation) use ($user) {
                return [
                    'id' => $explanation->id,
                    'date' => $explanation->date->tz('Asia/Ho_Chi_Minh')->startOfDay()->format('Y-m-d\TH:i:s.uP'),
                    'explanation' => $explanation->explanation,
                    'explanation_type' => $explanation->explanation_type,
                    'explanation_type_text' => $explanation->explanation_type_text,
                    'ot_hours' => $explanation->ot_hours,
                    'remote_shift' => $explanation->remoteShift ? [
                        'id' => $explanation->remoteShift->id,
                        'name' => $explanation->remoteShift->name,
                        'start_time' => $explanation->remoteShift->start_time,
                        'end_time' => $explanation->remoteShift->end_time,
                    ] : null,
                    'final_status' => $explanation->final_status,
                    'manager_status' => $explanation->manager_status,
                    'hr_status' => $explanation->hr_status,
                    'manager_approved_at' => $explanation->manager_approved_at ? $explanation->manager_approved_at->tz('Asia/Ho_Chi_Minh')->format('Y-m-d\TH:i:s.uP') : null,
                    'hr_approved_at' => $explanation->hr_approved_at ? $explanation->hr_approved_at->tz('Asia/Ho_Chi_Minh')->format('Y-m-d\TH:i:s.uP') : null,
                    'manager_note' => $explanation->manager_note,
                    'hr_note' => $explanation->hr_note,
                    'tagged_user_id' => $explanation->tagged_user_id,
                    'tagged_user_name' => $explanation->taggedUser ? $explanation->taggedUser->name : null,
                    'tagged_user_status' => $explanation->tagged_user_status,
                    'tagged_user_confirmed_at' => $explanation->tagged_user_confirmed_at ? $explanation->tagged_user_confirmed_at->tz('Asia/Ho_Chi_Minh')->format('Y-m-d\TH:i:s.uP') : null,
                    'tagged_user_note' => $explanation->tagged_user_note,
                    'created_at' => $explanation->created_at->tz('Asia/Ho_Chi_Minh')->format('Y-m-d\TH:i:s.uP'),
                    'can_update' => $this->attendanceExplanationService->canUpdateExplanation($user, $explanation)['allowed'] ?? false,
                    'can_delete' => $this->attendanceExplanationService->canDeleteExplanation($user, $explanation)['allowed'] ?? false,
                ];
            });

            // Format pagination data
            $paginationData = [
                'current_page' => $explanations->currentPage(),
                'last_page' => $explanations->lastPage(),
                'per_page' => $explanations->perPage(),
                'total' => $explanations->total(),
                'from' => $explanations->firstItem(),
                'to' => $explanations->lastItem(),
                'has_more_pages' => $explanations->hasMorePages(),
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'explanations' => $formattedExplanations,
                    'pagination' => $paginationData,
                    'filters' => [
                        'status' => $status,
                        'explanation_type' => $explanationTypes,
                        'date_from' => $dateFrom,
                        'date_to' => $dateTo,
                        'month' => $month,
                        'year' => $year,
                        'sort_by' => $sortBy,
                        'sort_order' => $sortOrder,
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in getMyExplanations: ' . $e->getMessage(), [
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tải danh sách giải trình: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Tiếp tục giải trình
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function resume($id)
    {
        try {
            $user = auth()->user();
            $explanation = AttendanceExplanation::where('id', $id)
                ->where('user_id', $user->id)
                ->first();

            if (!$explanation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy giải trình hoặc bạn không có quyền truy cập'
                ], 404);
            }

            $result = $this->attendanceExplanationService->resumeExplanation($user, $explanation);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message']
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => $result['message']
            ], 400);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lấy count của các loại giải trình
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getExplanationCounts(Request $request)
    {
        try {
            $user = auth()->user();

            // 1. Count giải trình (không bao gồm OT)
            $explanationCount = AttendanceExplanation::where('user_id', $user->id)
                ->where('explanation_type', '!=', 'overtime')
                ->count();

            // 2. Count OT (chỉ đếm của OT)
            $otCount = AttendanceExplanation::where('user_id', $user->id)
                ->where('explanation_type', 'overtime')
                ->count();

            // 3. Count giải trình đang gửi đến cho bạn duyệt (manager)
            $managerPendingCount = AttendanceExplanation::whereHas('user', function($query) use ($user) {
                    $query->whereHas('managers', function($subQuery) use ($user) {
                        $subQuery->where('user_managers.manager_user_id', $user->id)
                                 ->whereNull('user_managers.deleted_at');
                    });
                })
                ->where('manager_status', 'pending')
                ->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'counts' => [
                        'explanations' => $explanationCount, // Giải trình (không bao gồm OT)
                        'overtime' => $otCount, // OT (chỉ đếm của OT)
                        'manager_pending' => $managerPendingCount, // Giải trình đang gửi đến cho bạn duyệt
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in getExplanationCounts: ' . $e->getMessage(), [
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tải dữ liệu: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lấy thông tin chi tiết các lần checkin/checkout trong ngày
     * 
     * @param Request $request
     * @param string $date
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDayCheckinCheckoutDetails(Request $request, string $date)
    {
        try {
            $user = auth()->user();
            $targetDate = Carbon::parse($date);

            // Lấy tất cả timesheets cho ngày này
            $timesheets = \App\Models\Timesheet::where('user_id', $user->id)
                ->where('date', $date)
                ->with(['shift', 'shiftRuleHistory', 'companyAddress', 'checkoutCompanyAddress'])
                ->orderBy('checkin')
                ->get();

            // Đảm bảo relationships được load đúng cách
            $timesheets->load(['companyAddress', 'checkoutCompanyAddress']);

            // Lấy timesheets của ngày hôm trước (để xử lý case checkin tối hôm trước)
            $previousDate = $targetDate->copy()->subDay();
            $previousTimesheets = \App\Models\Timesheet::where('user_id', $user->id)
                ->where('date', $previousDate->format('Y-m-d'))
                ->with(['shift', 'shiftRuleHistory', 'companyAddress', 'checkoutCompanyAddress'])
                ->whereNotNull('checkin')
                ->whereNull('checkout') // Chỉ lấy những timesheet chưa checkout
                ->get();

            // Đảm bảo relationships được load đúng cách
            $previousTimesheets->load(['companyAddress', 'checkoutCompanyAddress']);

            // Lấy timesheets của ngày hôm sau (để xử lý case checkout sáng hôm sau)
            $nextDate = $targetDate->copy()->addDay();
            $nextTimesheets = \App\Models\Timesheet::where('user_id', $user->id)
                ->where('date', $nextDate->format('Y-m-d'))
                ->with(['shift', 'shiftRuleHistory', 'companyAddress', 'checkoutCompanyAddress'])
                ->whereNotNull('checkout')
                ->whereNull('checkin') // Chỉ lấy những timesheet chưa checkin
                ->get();

            // Đảm bảo relationships được load đúng cách
            $nextTimesheets->load(['companyAddress', 'checkoutCompanyAddress']);

            $checkinCheckoutRecords = [];

            // Xử lý timesheets của ngày hiện tại
            foreach ($timesheets as $timesheet) {
                $checkinCheckoutRecords[] = [
                    'id' => $timesheet->id,
                    'date' => $timesheet->date->format('Y-m-d'),
                    'checkin' => $timesheet->checkin ? $timesheet->checkin->format('Y-m-d H:i:s') : null,
                    'checkout' => $timesheet->checkout ? $timesheet->checkout->format('Y-m-d H:i:s') : null,
                    'shift_name' => $timesheet->shift ? $timesheet->shift->name : null,
                    'checkin_company_address' => $this->formatCompanyAddress($timesheet->companyAddress),
                    'checkout_company_address' => $this->formatCompanyAddress($timesheet->checkoutCompanyAddress),
                    'type' => 'current_day'
                ];
            }

            // Xử lý timesheets của ngày hôm trước (checkin tối hôm trước)
            foreach ($previousTimesheets as $timesheet) {
                $checkinCheckoutRecords[] = [
                    'id' => $timesheet->id,
                    'date' => $timesheet->date->format('Y-m-d'),
                    'checkin' => $timesheet->checkin ? $timesheet->checkin->format('Y-m-d H:i:s') : null,
                    'checkout' => $timesheet->checkout ? $timesheet->checkout->format('Y-m-d H:i:s') : null,
                    'shift_name' => $timesheet->shift ? $timesheet->shift->name : null,
                    'checkin_company_address' => $this->formatCompanyAddress($timesheet->companyAddress),
                    'checkout_company_address' => $this->formatCompanyAddress($timesheet->checkoutCompanyAddress),
                    'type' => 'previous_day_checkin'
                ];
            }

            // Xử lý timesheets của ngày hôm sau (checkout sáng hôm sau)
            foreach ($nextTimesheets as $timesheet) {
                $checkinCheckoutRecords[] = [
                    'id' => $timesheet->id,
                    'date' => $timesheet->date->format('Y-m-d'),
                    'checkin' => $timesheet->checkin ? $timesheet->checkin->format('Y-m-d H:i:s') : null,
                    'checkout' => $timesheet->checkout ? $timesheet->checkout->format('Y-m-d H:i:s') : null,
                    'shift_name' => $timesheet->shift ? $timesheet->shift->name : null,
                    'checkin_company_address' => $this->formatCompanyAddress($timesheet->companyAddress),
                    'checkout_company_address' => $this->formatCompanyAddress($timesheet->checkoutCompanyAddress),
                    'type' => 'next_day_checkout'
                ];
            }

            // Sắp xếp theo thời gian checkin
            usort($checkinCheckoutRecords, function($a, $b) {
                $timeA = $a['checkin'] ? strtotime($a['checkin']) : 0;
                $timeB = $b['checkin'] ? strtotime($b['checkin']) : 0;
                return $timeA - $timeB;
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'date' => $date,
                    'records' => $checkinCheckoutRecords,
                    'total_records' => count($checkinCheckoutRecords)
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in getDayCheckinCheckoutDetails: ' . $e->getMessage(), [
                'date' => $date,
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tải dữ liệu: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lấy thông tin timesheet và shift cho validation shift change
     *
     * @param Request $request
     * @param string $date
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTimesheetForShiftValidation(Request $request, string $date)
    {
        try {
            $user = auth()->user();

            // Lấy timesheet cho ngày này
            $timesheets = \App\Models\Timesheet::where('user_id', $user->id)
                ->where('date', $date)
                ->with(['shift', 'shiftRuleHistory'])
                ->get();

            if ($timesheets->isEmpty()) {
                return response()->json([
                    'success' => true,
                    'data' => [
                        'has_timesheet' => false,
                        'message' => 'Không có dữ liệu chấm công cho ngày này'
                    ]
                ]);
            }

            // Lấy checkin sớm nhất và checkout muộn nhất
            $earliestCheckin = $timesheets->filter(function($t) { return $t->checkin; })->min('checkin');
            $latestCheckout = $timesheets->filter(function($t) { return $t->checkout; })->max('checkout');

            // Lấy shift hiện tại (từ timesheet đầu tiên)
            $currentTimesheet = $timesheets->first();
            $currentShift = $currentTimesheet->shift;

            // Sử dụng getShiftRule() để lấy rule phù hợp với ngày
            $currentShiftRule = $currentTimesheet->getShiftRule();

            $currentShiftData = null;
            if ($currentShift) {
                $currentShiftData = [
                    'id' => $currentShift->id,
                    'name' => $currentShift->name,
                    'start_time' => $currentShiftRule ? $currentShiftRule->start_time : $currentShift->start_time,
                    'end_time' => $currentShiftRule ? $currentShiftRule->end_time : $currentShift->end_time,
                    'rule_effective_from' => $currentShiftRule ? $currentShiftRule->effective_from : null,
                    'rule_effective_to' => $currentShiftRule ? $currentShiftRule->effective_to : null,
                ];
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'has_timesheet' => true,
                    'earliest_checkin' => $earliestCheckin ? $earliestCheckin->format('H:i') : null,
                    'latest_checkout' => $latestCheckout ? $latestCheckout->format('H:i') : null,
                    'current_shift' => $currentShiftData,
                    'total_timesheets' => $timesheets->count()
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Error getting timesheet for shift validation: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Format company address data safely
     */
    private function formatCompanyAddress($companyAddress)
    {
        if (!$companyAddress) {
            Log::debug('Company address is null or empty');
            return null;
        }

        try {
            $formatted = [
                'id' => $companyAddress->id,
                'name' => $companyAddress->name,
                'lat' => $companyAddress->lat,
                'long' => $companyAddress->long
            ];

            Log::debug('Successfully formatted company address:', $formatted);
            return $formatted;

        } catch (\Exception $e) {
            Log::error('Error formatting company address:', [
                'error' => $e->getMessage(),
                'company_address_type' => get_class($companyAddress),
                'company_address_id' => $companyAddress->id ?? 'unknown',
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Lấy danh sách ca làm việc với thông tin từ shift_rule_histories cho ngày cụ thể
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getShiftsWithRules(Request $request)
    {
        try {
            $user = auth()->user();
            $date = $request->get('date', now()->format('Y-m-d'));

            \Log::info('Getting shifts with rules', [
                'user_id' => $user->id,
                'date' => $date
            ]);

            // Validate date format
            try {
                \Carbon\Carbon::parse($date);
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => 'Định dạng ngày không hợp lệ'
                ], 400);
            }

            // Lấy shifts với rules cho ngày cụ thể
            $shiftsWithRules = $user->getAvailableShiftsWithRules($date);

            if ($shiftsWithRules->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy ca làm việc nào cho user này',
                    'data' => []
                ], 404);
            }

            $formattedShifts = $shiftsWithRules->map(function ($shift) {
                // Đảm bảo tất cả properties đều có giá trị
                $startTime = $shift->start_time ?? '00:00:00';
                $endTime = $shift->end_time ?? '00:00:00';
                $hasRule = $shift->has_rule ?? false;

                return [
                    'id' => $shift->id,
                    'name' => $shift->name,
                    'start_time' => $startTime,
                    'end_time' => $endTime,
                    'original_start_time' => $shift->original_start_time ?? $startTime,
                    'original_end_time' => $shift->original_end_time ?? $endTime,
                    'has_rule' => $hasRule,
                    'rule_effective_from' => $shift->rule_effective_from,
                    'rule_effective_to' => $shift->rule_effective_to,
                    'display_name' => $shift->name . ' (' . $startTime . ' - ' . $endTime . ')',
                    'display_with_rule_info' => $hasRule
                        ? $shift->name . ' (' . $startTime . ' - ' . $endTime . ') [Theo quy tắc mới]'
                        : $shift->name . ' (' . $startTime . ' - ' . $endTime . ')',
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $formattedShifts,
                'date' => $date
            ]);

        } catch (\Exception $e) {
            \Log::error('Error getting shifts with rules: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }
}
