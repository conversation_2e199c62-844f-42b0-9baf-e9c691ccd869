<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\AttendanceExplanation;
use App\Models\User;
use App\Notifications\AttendanceExplanationTaggedNotification;
use App\Services\AttendanceExplanation\AttendanceExplanationService;
use Illuminate\Http\Request;
use Carbon\Carbon;

class AttendanceExplanationController extends Controller
{
    protected AttendanceExplanationService $attendanceExplanationService;

    public function __construct(AttendanceExplanationService $attendanceExplanationService)
    {
        $this->attendanceExplanationService = $attendanceExplanationService;
    }

    /**
     * Hiển thị calendar chấm công theo tháng
     */
    public function index(Request $request)
    {
        $user = staff();

        // Lấy tháng/năm từ request hoặc mặc định là tháng hiện tại
        $month = $request->get('month', now()->month);
        $year = $request->get('year', now()->year);

        // Validate tháng/năm
        $month = max(1, min(12, (int)$month));
        $year = max(2020, min(2030, (int)$year));

        $currentDate = Carbon::create($year, $month, 1);

        // Lấy dữ liệu chấm công cho calendar (bao gồm tháng trước/sau)
        $attendanceData = $this->attendanceExplanationService->getMonthlyAttendanceForCalendar($user, $currentDate);

        // Tạo calendar data
        $calendarData = $this->attendanceExplanationService->generateCalendarData($currentDate, $attendanceData);

        // Lấy manager mặc định
        $defaultManager = $this->getDefaultManagerInfo($user);

        // Lấy danh sách đồng nghiệp (những user cùng được quản lý bởi manager của user hiện tại)
        $colleagues = $this->getColleagues($user);

        // Lấy thống kê giải trình trong tháng
        $monthlyStats = $this->attendanceExplanationService->getMonthlyExplanationStats($user, $currentDate);

        return view('pages.attendance-explanation.index', [
            'user' => $user,
            'currentDate' => $currentDate,
            'calendarData' => $calendarData,
            'attendanceData' => $attendanceData,
            'month' => $month,
            'year' => $year,
            'prevMonth' => $currentDate->copy()->subMonth(),
            'nextMonth' => $currentDate->copy()->addMonth(),
            'defaultManager' => $defaultManager,
            'colleagues' => $colleagues,
            'monthlyStats' => $monthlyStats,
        ]);
    }

    /**
     * Hiển thị form giải trình cho ngày cụ thể
     */
    public function show(Request $request, string $date)
    {
        $user = staff();
        $targetDate = Carbon::parse($date);

        // Lấy thông tin chấm công cho ngày
        $attendanceInfo = $this->attendanceExplanationService->getDayAttendance($user, $targetDate);

        if (!$attendanceInfo) {
            abort(404, 'Không tìm thấy dữ liệu chấm công cho ngày này');
        }

        return view('pages.attendance-explanation.show', [
            'user' => $user,
            'date' => $targetDate,
            'attendanceInfo' => $attendanceInfo,
        ]);
    }

    /**
     * Lưu giải trình chấm công
     */
    public function store(Request $request)
    {
        $user = staff();

        // Lấy danh sách shift IDs mà user được phép sử dụng
        $availableShiftIds = $user->getAvailableShifts()->pluck('id')->toArray();

        $request->validate([
            'date' => 'required|date',
            'explanation' => 'required|string|max:500',
            'explanation_type' => 'required|in:late,early,insufficient_hours,no_checkin,no_checkout,overtime,remote_work,shift_change,new_employee_no_account,other',
            'ot_hours' => 'nullable|numeric|min:0.1|max:24|required_if:explanation_type,overtime',
            'remote_shift_id' => [
                'nullable',
                'required_if:explanation_type,remote_work',
                function ($attribute, $value, $fail) use ($availableShiftIds) {
                    if ($value && !in_array($value, $availableShiftIds)) {
                        $fail('Ca làm việc được chọn không hợp lệ hoặc bạn không có quyền sử dụng.');
                    }
                }
            ],
            'shift_change_id' => [
                'nullable',
                'required_if:explanation_type,shift_change',
                function ($attribute, $value, $fail) use ($availableShiftIds) {
                    if ($value && !in_array($value, $availableShiftIds)) {
                        $fail('Ca làm việc được chọn không hợp lệ hoặc bạn không có quyền sử dụng.');
                    }
                }
            ],
            'tagged_user_id' => [
                'nullable',
                'exists:users,id',
                function ($attribute, $value, $fail) {
                    if ($value) {
                        $user = User::find($value);
                        if (!$user || !$user->active_flag || $user->deleted_at) {
                            $fail('User được chọn không hợp lệ hoặc không còn hoạt động.');
                        }
                    }
                }
            ],
        ]);
        $date = Carbon::parse($request->date);

        try {
            // Kiểm tra giới hạn 3 giải trình/tháng (trừ OT, remote work, shift_change, new_employee_no_account và other)
            if (!in_array($request->explanation_type, ['overtime', 'remote_work', 'shift_change', 'new_employee_no_account', 'other'])) {
                $canCreate = $this->attendanceExplanationService->canCreateExplanation($user, $date);
                if (!$canCreate['allowed']) {
                    return response()->json([
                        'success' => false,
                        'message' => $canCreate['message']
                    ], 400);
                }
            }

            // Xác định trạng thái ban đầu dựa trên có tagged user hay không
            $initialFinalStatus = $request->tagged_user_id ? 'pending_tagged_user' : 'pending';

            // Tạo giải trình
            $explanationData = [
                'user_id' => $user->id,
                'date' => $date->format('Y-m-d'),
                'explanation' => $request->explanation,
                'explanation_type' => $request->explanation_type,
                'ot_hours' => $request->ot_hours,
                'remote_shift_id' => $request->remote_shift_id,
                'shift_change_id' => $request->shift_change_id,
                'created_by' => $user->id,
                'manager_status' => 'pending',
                'hr_status' => 'pending',
                'final_status' => $initialFinalStatus,
            ];

            // Chỉ set tagged_user_id và tagged_user_status khi có tagged user
            if ($request->tagged_user_id) {
                $explanationData['tagged_user_id'] = $request->tagged_user_id;
                $explanationData['tagged_user_status'] = 'pending';
            } else {
                // Đảm bảo tagged_user_status là NULL khi không có tagged user
                $explanationData['tagged_user_status'] = null;
            }

            $explanation = AttendanceExplanation::create($explanationData);

            $result = $explanation ? true : false;

            if ($result) {
                // Gửi notification cho user được tag (nếu có)
                if ($request->tagged_user_id) {
                    $taggedUser = User::find($request->tagged_user_id);
                    if ($taggedUser) {
                        $taggedUser->notify(new AttendanceExplanationTaggedNotification($explanation, $user));
                    }
                }

                return response()->json([
                    'success' => true,
                    'message' => 'Đã lưu giải trình thành công'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi lưu giải trình'
            ], 500);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * API endpoint để lấy thông tin chấm công cho một ngày
     */
    public function getAttendanceInfo(Request $request, string $date)
    {
        $user = staff();
        $targetDate = Carbon::parse($date);

        $attendanceInfo = $this->attendanceExplanationService->getDayAttendance($user, $targetDate);

        return response()->json([
            'success' => true,
            'data' => $attendanceInfo
        ]);
    }

    /**
     * Lấy thông tin manager mặc định
     */
    protected function getDefaultManagerInfo($user)
    {
        $manager = \DB::table('user_managers')
            ->join('users', 'users.id', '=', 'user_managers.manager_user_id')
            ->where('user_managers.user_id', $user->id)
            ->whereNull('user_managers.deleted_at')
            ->whereNull('users.deleted_at')
            ->where('users.active_flag', true)
            ->select('users.id', 'users.name', 'users.phone_number')
            ->first();

        return $manager;
    }

    /**
     * Lấy danh sách đồng nghiệp (những user cùng được quản lý bởi manager của user hiện tại)
     */
    protected function getColleagues($user)
    {
        // Tìm manager của user hiện tại
        $managerInfo = $this->getDefaultManagerInfo($user);

        if (!$managerInfo) {
            return collect(); // Không có manager thì không có đồng nghiệp
        }

        // Lấy tất cả users được quản lý bởi manager này (trừ chính user hiện tại)
        $colleagueIds = \DB::table('user_managers')
            ->where('manager_user_id', $managerInfo->id)
            ->where('user_id', '!=', $user->id) // Loại trừ chính user hiện tại
            ->whereNull('deleted_at')
            ->pluck('user_id');

        return \App\Models\User::whereIn('id', $colleagueIds)
            ->where('active_flag', true)
            ->whereNull('deleted_at')
            ->with(['staffDepartment'])
            ->select('id', 'name', 'phone_number', 'account', 'staff_department_id')
            ->orderBy('name')
            ->get();
    }

    /**
     * Hiển thị trang xác nhận giải trình cho tagged user
     */
    public function taggedConfirmations(Request $request)
    {
        return view('pages.attendance-explanation.tagged-confirmations');
    }

    /**
     * Lấy danh sách giải trình có tag user hiện tại
     */
    public function getTaggedExplanations(Request $request)
    {
        $user = staff();
        $employeeName = $request->get('employee_name');
        $month = $request->get('month');
        $year = $request->get('year', date('Y'));

        $query = AttendanceExplanation::where('tagged_user_id', $user->id)
            ->with(['user', 'creator']);

        // Filter by employee name (creator)
        if ($employeeName) {
            $query->whereHas('creator', function($q) use ($employeeName) {
                $q->where('name', 'like', '%' . $employeeName . '%');
            });
        }

        // Filter by month
        if ($month) {
            $query->whereMonth('date', $month);
        }

        // Filter by year
        if ($year) {
            $query->whereYear('date', $year);
        }

        $explanations = $query->orderBy('date', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => $explanations->map(function ($explanation) {
                return [
                    'id' => $explanation->id,
                    'date' => $explanation->date->format('Y-m-d'),
                    'explanation' => $explanation->explanation,
                    'explanation_type' => $explanation->explanation_type,
                    'explanation_type_text' => $explanation->explanation_type_text,
                    'ot_hours' => $explanation->ot_hours,
                    'user_name' => $explanation->user->name ?? 'N/A',
                    'creator_name' => $explanation->creator->name ?? 'N/A',
                    'created_by' => $explanation->created_by,
                    'tagged_user_status' => $explanation->tagged_user_status,
                    'tagged_user_status_text' => $explanation->tagged_user_status_text,
                    'created_at' => $explanation->created_at->format('Y-m-d H:i:s'),
                ];
            })
        ]);
    }

    /**
     * Lấy lịch sử xác nhận giải trình
     */
    public function getTaggedHistory(Request $request)
    {
        $user = staff();
        $employeeName = $request->get('employee_name');
        $status = $request->get('status');
        $month = $request->get('month');

        $query = AttendanceExplanation::where('tagged_user_id', $user->id)
            ->whereIn('tagged_user_status', ['confirmed', 'rejected'])
            ->whereNotNull('tagged_user_confirmed_at')
            ->with(['user', 'creator']);

        // Filter by employee name (creator)
        if ($employeeName) {
            $query->whereHas('creator', function($q) use ($employeeName) {
                $q->where('name', 'like', '%' . $employeeName . '%');
            });
        }

        // Filter by status
        if ($status) {
            $query->where('tagged_user_status', $status);
        }

        // Filter by confirmation month
        if ($month) {
            $query->whereMonth('tagged_user_confirmed_at', $month);
        }

        $explanations = $query->orderBy('tagged_user_confirmed_at', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => $explanations->map(function ($explanation) {
                return [
                    'id' => $explanation->id,
                    'date' => $explanation->date->format('Y-m-d'),
                    'explanation' => $explanation->explanation,
                    'explanation_type' => $explanation->explanation_type,
                    'explanation_type_text' => $explanation->explanation_type_text,
                    'ot_hours' => $explanation->ot_hours,
                    'user_name' => $explanation->user->name ?? 'N/A',
                    'creator_name' => $explanation->creator->name ?? 'N/A',
                    'created_by' => $explanation->created_by,
                    'tagged_user_status' => $explanation->tagged_user_status,
                    'tagged_user_status_text' => $explanation->tagged_user_status_text,
                    'tagged_user_note' => $explanation->tagged_user_note,
                    'tagged_user_confirmed_at' => $explanation->tagged_user_confirmed_at->format('Y-m-d H:i:s'),
                    'created_at' => $explanation->created_at->format('Y-m-d H:i:s'),
                ];
            })
        ]);
    }

    /**
     * Cập nhật giải trình chấm công (chỉ khi chưa có ai duyệt)
     */
    public function update(Request $request, AttendanceExplanation $attendanceExplanation)
    {
        $user = staff();

        // Kiểm tra quyền update (chỉ người tạo)
        if ($attendanceExplanation->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Bạn không có quyền cập nhật giải trình này'
            ], 403);
        }

        // Kiểm tra có thể update không (chưa có ai duyệt)
        $canUpdate = $this->attendanceExplanationService->canUpdateExplanation($user, $attendanceExplanation);
        if (!$canUpdate['allowed']) {
            return response()->json([
                'success' => false,
                'message' => $canUpdate['message']
            ], 400);
        }

        // Lấy danh sách shift IDs mà user được phép sử dụng
        $availableShiftIds = $user->getAvailableShifts()->pluck('id')->toArray();

        $request->validate([
            'explanation' => 'required|string|max:500',
            'explanation_type' => 'required|in:late,early,insufficient_hours,no_checkin,no_checkout,overtime,remote_work,shift_change,new_employee_no_account,other',
            'ot_hours' => 'nullable|numeric|min:0.1|max:24|required_if:explanation_type,overtime',
            'remote_shift_id' => [
                'nullable',
                'required_if:explanation_type,remote_work',
                function ($attribute, $value, $fail) use ($availableShiftIds) {
                    if ($value && !in_array($value, $availableShiftIds)) {
                        $fail('Ca làm việc được chọn không hợp lệ hoặc bạn không có quyền sử dụng.');
                    }
                }
            ],
            'shift_change_id' => [
                'nullable',
                'required_if:explanation_type,shift_change',
                function ($attribute, $value, $fail) use ($availableShiftIds) {
                    if ($value && !in_array($value, $availableShiftIds)) {
                        $fail('Ca làm việc được chọn không hợp lệ hoặc bạn không có quyền sử dụng.');
                    }
                }
            ],
            'tagged_user_id' => 'nullable|exists:users,id',
        ]);

        try {
            $result = $this->attendanceExplanationService->updateExplanation(
                $user,
                $attendanceExplanation,
                $request->all()
            );

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Đã cập nhật giải trình thành công'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => $result['message']
            ], 400);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lấy thông tin giải trình để edit
     */
    public function edit(AttendanceExplanation $attendanceExplanation)
    {
        $user = staff();

        // Kiểm tra quyền edit (chỉ người tạo hoặc tagged user)
        $isOwner = $attendanceExplanation->user_id === $user->id;
        $isTaggedUser = $attendanceExplanation->tagged_user_id === $user->id;

        if (!$isOwner && !$isTaggedUser) {
            return response()->json([
                'success' => false,
                'message' => 'Bạn không có quyền xem giải trình này'
            ], 403);
        }

        // Nếu là owner, kiểm tra có thể edit không
        $canUpdate = false;
        if ($isOwner) {
            $canUpdateResult = $this->attendanceExplanationService->canUpdateExplanation($user, $attendanceExplanation);
            $canUpdate = $canUpdateResult['allowed'];

            if (!$canUpdate && !$isTaggedUser) {
                return response()->json([
                    'success' => false,
                    'message' => $canUpdateResult['message']
                ], 400);
            }
        }

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $attendanceExplanation->id,
                'date' => $attendanceExplanation->date->format('Y-m-d'),
                'explanation' => $attendanceExplanation->explanation,
                'explanation_type' => $attendanceExplanation->explanation_type,
                'explanation_type_text' => $attendanceExplanation->explanation_type_text,
                'ot_hours' => $attendanceExplanation->ot_hours,
                'remote_shift_id' => $attendanceExplanation->remote_shift_id,
                'tagged_user_id' => $attendanceExplanation->tagged_user_id,
                'tagged_user' => $attendanceExplanation->taggedUser ? [
                    'id' => $attendanceExplanation->taggedUser->id,
                    'name' => $attendanceExplanation->taggedUser->name,
                    'account' => $attendanceExplanation->taggedUser->account,
                ] : null,
                'user_name' => $attendanceExplanation->user->name,
                'creator_name' => $attendanceExplanation->creator->name,
                'final_status' => $attendanceExplanation->final_status,
                'can_update' => $canUpdate,
                'is_owner' => $isOwner,
                'is_tagged_user' => $isTaggedUser,
            ]
        ]);
    }

    /**
     * Tagged User xác nhận giải trình (Bước 0)
     */
    public function taggedUserConfirm(Request $request, AttendanceExplanation $attendanceExplanation)
    {
        $user = staff();

        // Kiểm tra quyền xác nhận (chỉ user được tag mới có quyền)
        if ($attendanceExplanation->tagged_user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Bạn không có quyền xác nhận giải trình này'
            ], 403);
        }

        // Kiểm tra trạng thái hiện tại
        if ($attendanceExplanation->tagged_user_status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'Giải trình này đã được xử lý rồi'
            ], 400);
        }

        $request->validate([
            'status' => 'required|in:confirmed,rejected',
            'note' => 'nullable|string|max:1000'
        ]);

        try {
            // Cập nhật trạng thái tagged user
            $attendanceExplanation->update([
                'tagged_user_status' => $request->status,
                'tagged_user_confirmed_at' => now(),
                'tagged_user_note' => $request->note,
            ]);

            // Cập nhật final status
            $attendanceExplanation->updateFinalStatus();

            $message = $request->status === 'confirmed'
                ? 'Đã xác nhận giải trình thành công'
                : 'Đã từ chối giải trình';

            return response()->json([
                'success' => true,
                'message' => $message
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Manager duyệt giải trình (Bước 1)
     */
    public function managerApprove(Request $request, AttendanceExplanation $attendanceExplanation)
    {
        $user = auth()->user();

        // Kiểm tra quyền duyệt manager
        if (!$this->attendanceExplanationService->canManagerApprove($user, $attendanceExplanation)) {
            return response()->json([
                'success' => false,
                'message' => 'Bạn không có quyền duyệt giải trình này'
            ], 403);
        }

        $request->validate([
            'status' => 'required|in:approved,rejected',
            'note' => 'nullable|string|max:1000'
        ]);

        try {
            $this->attendanceExplanationService->managerApprove(
                $attendanceExplanation,
                $user,
                $request->status,
                $request->note
            );

            return response()->json([
                'success' => true,
                'message' => $request->status === 'approved' ? 'Manager đã duyệt giải trình' : 'Manager đã từ chối giải trình'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * HR duyệt giải trình (Bước 2 - chỉ R080)
     */
    public function hrApprove(Request $request, AttendanceExplanation $attendanceExplanation)
    {
        $user = auth()->user();

        // Kiểm tra quyền duyệt HR (chỉ R080)
        if (!$this->attendanceExplanationService->canHrApprove($user, $attendanceExplanation)) {
            return response()->json([
                'success' => false,
                'message' => 'Bạn không có quyền duyệt HR cho giải trình này'
            ], 403);
        }

        $request->validate([
            'status' => 'required|in:approved,rejected',
            'note' => 'nullable|string|max:1000'
        ]);

        try {
            $this->attendanceExplanationService->hrApprove(
                $attendanceExplanation,
                $user,
                $request->status,
                $request->note
            );

            return response()->json([
                'success' => true,
                'message' => $request->status === 'approved' ? 'HR đã duyệt giải trình - Hoàn tất!' : 'HR đã từ chối giải trình'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Xóa giải trình (soft delete)
     */
    public function destroy(AttendanceExplanation $attendanceExplanation)
    {
        $user = staff();

        $result = $this->attendanceExplanationService->deleteExplanation($user, $attendanceExplanation);

        if ($result['success']) {
            return response()->json($result);
        }

        return response()->json($result, 403);
    }

    /**
     * Tạm dừng giải trình
     */
    public function pause(AttendanceExplanation $attendanceExplanation)
    {
        $user = staff();

        $result = $this->attendanceExplanationService->pauseExplanation($user, $attendanceExplanation);

        if ($result['success']) {
            return response()->json($result);
        }

        return response()->json($result, 403);
    }

    /**
     * Tiếp tục giải trình đã tạm dừng
     */
    public function resume(AttendanceExplanation $attendanceExplanation)
    {
        $user = staff();

        $result = $this->attendanceExplanationService->resumeExplanation($user, $attendanceExplanation);

        if ($result['success']) {
            return response()->json($result);
        }

        return response()->json($result, 403);
    }

    /**
     * Bulk confirm/reject tagged explanations
     */
    public function bulkTaggedConfirm(Request $request)
    {
        $request->validate([
            'explanation_ids' => 'required|array|min:1',
            'explanation_ids.*' => 'required|integer|exists:attendance_explanations,id',
            'status' => 'required|in:confirmed,rejected',
            'note' => 'nullable|string|max:1000'
        ]);

        $user = auth()->user();
        $explanationIds = $request->explanation_ids;
        $status = $request->status;
        $note = $request->note;

        try {
            \DB::beginTransaction();

            // Lấy các giải trình mà user được tag để confirm
            $explanations = AttendanceExplanation::whereIn('id', $explanationIds)
                ->where('tagged_user_id', $user->id)
                ->where('tagged_user_status', 'pending')
                ->get();

            if ($explanations->count() !== count($explanationIds)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Một số giải trình không hợp lệ hoặc đã được xử lý'
                ], 400);
            }

            $processedCount = 0;
            $now = now();

            foreach ($explanations as $explanation) {
                // Cập nhật trạng thái tagged user
                \DB::table('attendance_explanations')
                    ->where('id', $explanation->id)
                    ->update([
                        'tagged_user_status' => $status,
                        'tagged_user_confirmed_at' => $now,
                        'tagged_user_note' => $note,
                        'updated_at' => $now
                    ]);

                $processedCount++;
            }

            \DB::commit();

            $actionText = $status === 'confirmed' ? 'xác nhận' : 'từ chối';
            $message = "Đã {$actionText} thành công {$processedCount} giải trình";

            return response()->json([
                'success' => true,
                'message' => $message,
                'processed_count' => $processedCount
            ]);

        } catch (\Exception $e) {
            \DB::rollback();

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Lấy thông tin timesheet và shift cho validation shift change (Web route)
     *
     * @param string $date
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTimesheetForShiftValidation(string $date)
    {
        try {
            $user = auth()->user();

            // Lấy timesheet cho ngày này
            $timesheets = \App\Models\Timesheet::where('user_id', $user->id)
                ->where('date', $date)
                ->with(['shift', 'shiftRuleHistory'])
                ->get();

            if ($timesheets->isEmpty()) {
                return response()->json([
                    'success' => true,
                    'data' => [
                        'has_timesheet' => false,
                        'message' => 'Không có dữ liệu chấm công cho ngày này'
                    ]
                ]);
            }

            // Lấy checkin sớm nhất và checkout muộn nhất
            $earliestCheckin = $timesheets->filter(function($t) { return $t->checkin; })->min('checkin');
            $latestCheckout = $timesheets->filter(function($t) { return $t->checkout; })->max('checkout');

            // Lấy shift hiện tại (từ timesheet đầu tiên)
            $currentTimesheet = $timesheets->first();
            $currentShift = $currentTimesheet->shift;

            // Sử dụng getShiftRule() để lấy rule phù hợp với ngày
            $currentShiftRule = $currentTimesheet->getShiftRule();

            $currentShiftData = null;
            if ($currentShift) {
                $currentShiftData = [
                    'id' => $currentShift->id,
                    'name' => $currentShift->name,
                    'start_time' => $currentShiftRule ? $currentShiftRule->start_time : $currentShift->start_time,
                    'end_time' => $currentShiftRule ? $currentShiftRule->end_time : $currentShift->end_time,
                    'rule_effective_from' => $currentShiftRule ? $currentShiftRule->effective_from : null,
                    'rule_effective_to' => $currentShiftRule ? $currentShiftRule->effective_to : null,
                ];
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'has_timesheet' => true,
                    'earliest_checkin' => $earliestCheckin ? $earliestCheckin->format('H:i') : null,
                    'latest_checkout' => $latestCheckout ? $latestCheckout->format('H:i') : null,
                    'current_shift' => $currentShiftData,
                    'total_timesheets' => $timesheets->count()
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Error getting timesheet for shift validation: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }
}
