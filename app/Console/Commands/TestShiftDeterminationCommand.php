<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Timesheet;
use App\Services\Timesheet\ShiftDeterminationService;
use Carbon\Carbon;

class TestShiftDeterminationCommand extends Command
{
    protected $signature = 'timesheet:test-shift-determination 
                            {user_id : ID của user để test}
                            {date : Ngày để test (Y-m-d)}
                            {--limit=10 : Số lượng timesheet để test}';

    protected $description = 'Test và so sánh kết quả shift determination với logic cũ';

    public function handle()
    {
        $userId = $this->argument('user_id');
        $date = $this->argument('date');
        $limit = $this->option('limit');

        $this->info("Testing shift determination for user {$userId} on {$date}");
        $this->info("Limit: {$limit} timesheets");
        $this->newLine();

        // Lấy timesheets của user trong ngày
        $timesheets = Timesheet::where('user_id', $userId)
            ->where('date', $date)
            ->with(['user.shifts', 'shift'])
            ->limit($limit)
            ->get();

        if ($timesheets->isEmpty()) {
            $this->error("Không tìm thấy timesheet nào cho user {$userId} trong ngày {$date}");
            return;
        }

        $shiftDeterminationService = new ShiftDeterminationService();

        $this->table(
            ['Timesheet ID', 'Checkin', 'Checkout', 'Ca hiện tại', 'Ca được xác định', 'Workday cũ', 'Workday mới', 'Khác biệt'],
            $timesheets->map(function ($timesheet) use ($shiftDeterminationService) {
                // Tính workday theo cách cũ
                $oldWorkday = $timesheet->calcWorkday();

                // Tính workday theo cách mới
                $newWorkday = $timesheet->calcWorkdayWithShiftDetermination();

                // Lấy ca được xác định
                $determinedShift = $shiftDeterminationService->determineWorkdayShift($timesheet);

                $difference = abs($oldWorkday - $newWorkday);
                $differenceText = $difference > 0.01 ? "⚠️ {$difference}" : "✅ Giống";

                return [
                    $timesheet->id,
                    $timesheet->checkin?->format('H:i:s') ?? 'N/A',
                    $timesheet->checkout?->format('H:i:s') ?? 'N/A',
                    $timesheet->shift->name ?? 'N/A',
                    $determinedShift?->name ?? 'N/A',
                    number_format($oldWorkday, 2),
                    number_format($newWorkday, 2),
                    $differenceText,
                ];
            })->toArray()
        );

        $this->newLine();
        $this->info("Giải thích:");
        $this->line("- ✅ Giống: Kết quả workday giống nhau");
        $this->line("- ⚠️ Khác: Có sự khác biệt trong tính toán workday");
        $this->newLine();

        // Hiển thị thông tin về các ca của user
        $user = $timesheets->first()->user;
        $this->info("Các ca làm việc của user {$user->name}:");
        foreach ($user->shifts as $shift) {
            $this->line("- {$shift->name}: {$shift->start_time} - {$shift->end_time}");
        }
    }
}
