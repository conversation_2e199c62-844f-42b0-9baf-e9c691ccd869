<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\Timesheet\SimpleWorkdayCalculator;
use Carbon\Carbon;

class TestSimpleWorkdayCommand extends Command
{
    protected $signature = 'timesheet:test-simple-workday';
    protected $description = 'Test Simple Workday Calculator với mock data';

    public function handle()
    {
        $this->info("=== TEST SIMPLE WORKDAY CALCULATOR ===");
        $this->newLine();

        $calculator = new SimpleWorkdayCalculator();

        // Test cases với mock data
        $testCases = [
            [
                'name' => 'Multiple timesheets đầy đủ',
                'timesheets' => [
                    $this->createMockTimesheet('08:30:00', '12:00:00'),
                    $this->createMockTimesheet('13:00:00', '17:30:00'),
                ],
            ],
            [
                'name' => 'Bản ghi cuối chỉ có checkin',
                'timesheets' => [
                    $this->createMockTimesheet('08:30:00', '12:00:00'),
                    $this->createMockTimesheet('13:00:00', null),
                ],
            ],
            [
                'name' => 'Multiple checkin/checkout không đều',
                'timesheets' => [
                    $this->createMockTimesheet('08:15:00', null),
                    $this->createMockTimesheet('09:00:00', '12:00:00'),
                    $this->createMockTimesheet('13:30:00', '18:00:00'),
                ],
            ],
            [
                'name' => 'Tất cả chỉ có checkin',
                'timesheets' => [
                    $this->createMockTimesheet('08:30:00', null),
                    $this->createMockTimesheet('12:00:00', null),
                    $this->createMockTimesheet('17:30:00', null),
                ],
            ],
        ];

        foreach ($testCases as $i => $testCase) {
            $this->info("--- {$testCase['name']} ---");
            
            // Hiển thị input
            $this->line("Input timesheets:");
            foreach ($testCase['timesheets'] as $j => $timesheet) {
                $checkin = $timesheet->checkin ? $timesheet->checkin->format('H:i') : 'N/A';
                $checkout = $timesheet->checkout ? $timesheet->checkout->format('H:i') : 'N/A';
                $this->line("  " . ($j + 1) . ". Checkin: {$checkin}, Checkout: {$checkout}");
            }

            // Tính toán
            $result = $calculator->calculateForMultipleTimesheets(collect($testCase['timesheets']));

            // Hiển thị kết quả
            $this->line("Kết quả:");
            $this->line("  Smart Checkin: " . ($result['smart_checkin']?->format('H:i') ?? 'N/A'));
            $this->line("  Smart Checkout: " . ($result['smart_checkout']?->format('H:i') ?? 'N/A'));
            $this->line("  Workday Hours: " . number_format($result['workday_hours'], 2));
            $this->line("  Workday: " . number_format($result['workday'], 2));
            $this->line("  Method: " . $result['method']);

            $this->newLine();
        }

        $this->info("=== SUMMARY ===");
        $this->line("✅ Logic đơn giản cho multiple timesheets:");
        $this->line("1. Lấy checkin sớm nhất");
        $this->line("2. So sánh checkout cuối vs checkin cuối → lấy thời gian muộn nhất");
        $this->line("3. Tính workday từ smart checkin → smart checkout");
        $this->newLine();

        $this->line("🎯 Để enable trong production:");
        $this->line("TIMESHEET_USE_SIMPLE_CALCULATOR=true");
    }

    private function createMockTimesheet($checkin, $checkout)
    {
        // Tạo mock object đơn giản
        return (object) [
            'checkin' => $checkin ? Carbon::createFromFormat('H:i:s', $checkin) : null,
            'checkout' => $checkout ? Carbon::createFromFormat('H:i:s', $checkout) : null,
            'date' => '2025-06-01',
            'shift' => (object) [
                'start_time' => '08:00:00',
                'end_time' => '17:00:00',
                'workday_min_1' => 7.5,
                'workday_min_2' => 4.5,
                'default_workday_threshold' => 1.0,
            ],
            'getShiftRule' => function() {
                return null; // Sử dụng shift gốc
            }
        ];
    }
}
