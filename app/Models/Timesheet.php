<?php

namespace App\Models;

use App\Contracts\Services\Timesheet\ShiftLogicTypeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Timesheet extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'date',
        'checkin',
        'checkout',
        'user_id',
        'shift_id',
        'company_address_id',
        'checkout_company_address_id',
        'shift_rule_history_id',
        'workday',
    ];

    protected $casts = [
        'date' => 'date',
        'checkin' => 'datetime',
        'checkout' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function shift()
    {
        return $this->belongsTo(Shift::class);
    }

    public function companyAddress()
    {
        return $this->belongsTo(CompanyAddress::class);
    }

    public function checkoutCompanyAddress()
    {
        return $this->belongsTo(CompanyAddress::class, 'checkout_company_address_id');
    }

    public function shiftRuleHistory()
    {
        return $this->belongsTo(ShiftRuleHistory::class);
    }

    /**
     * L<PERSON>y quy tắc ca làm việc áp dụng cho timesheet này
     *
     * @return ShiftRuleHistory
     */
    public function getShiftRule()
    {
        // Nếu đã lưu shift_rule_history_id, ưu tiên sử dụng
        if ($this->shift_rule_history_id) {
            return $this->shiftRuleHistory;
        }

        // Nếu chưa lưu, lấy quy tắc phù hợp với ngày của timesheet
        return $this->shift->getActiveRule($this->date);
    }

    public function calcWorkDayHours(): float
    {
        // Kiểm tra shift có tồn tại không
        if (!$this->shift) {
            return 0;
        }

        try {
            /** @var ShiftLogicTypeInterface $shiftLogicService */
            $shiftLogicService = $this->shift->getLogicTypeService();

            if (!$shiftLogicService) {
                \Log::error('ShiftLogicService is null for shift', [
                    'shift_id' => $this->shift->id,
                    'timesheet_id' => $this->id
                ]);
                return 0;
            }
        } catch (\Exception $e) {
            \Log::error('Error getting ShiftLogicService', [
                'shift_id' => $this->shift->id,
                'timesheet_id' => $this->id,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
        $shiftRule = $this->getShiftRule();

        if (!$shiftRule) {
            // Fallback nếu không tìm thấy quy tắc - không sử dụng hardcoded break times
            return $shiftLogicService->calcWorkdayHours([
                'schedule_start_time' => $this->shift->start_time,
                'schedule_end_time' => $this->shift->end_time,
                'start_time' => $this->checkin?->format('H:i:s'),
                'end_time' => $this->checkout?->format('H:i:s'),
                'break_times' => null, // No break times if not configured in shift rule
                'workday_min_1' => $this->shift->workday_min_1,
                'workday_min_2' => $this->shift->workday_min_2,
            ]);
        }

        return $shiftLogicService->calcWorkdayHours([
            'schedule_start_time' => $shiftRule->start_time,
            'schedule_end_time' => $shiftRule->end_time,
            'start_time' => $this->checkin?->format('H:i:s'),
            'end_time' => $this->checkout?->format('H:i:s'),
            'break_times' => $shiftRule->break_times,
            'workday_min_1' => $shiftRule->workday_min_1,
            'workday_min_2' => $shiftRule->workday_min_2,
        ]);
    }

    public function calcWorkday(): float
    {
        // Kiểm tra shift có tồn tại không
        if (!$this->shift) {
            return 0;
        }

        try {
            /** @var ShiftLogicTypeInterface $shiftLogicService */
            $shiftLogicService = $this->shift->getLogicTypeService();

            if (!$shiftLogicService) {
                \Log::error('ShiftLogicService is null for shift in calcWorkday', [
                    'shift_id' => $this->shift->id,
                    'timesheet_id' => $this->id
                ]);
                return 0;
            }
        } catch (\Exception $e) {
            \Log::error('Error getting ShiftLogicService in calcWorkday', [
                'shift_id' => $this->shift->id,
                'timesheet_id' => $this->id,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
        $shiftRule = $this->getShiftRule();

        if (!$shiftRule) {
            // Fallback nếu không tìm thấy quy tắc
            return $shiftLogicService->calcWorkday([
                'schedule_start_time' => $this->shift->start_time,
                'schedule_end_time' => $this->shift->end_time,
                'start_time' => $this->checkin?->format('H:i:s'),
                'end_time' => $this->checkout?->format('H:i:s'),
                'workday_min_1' => $this->shift->workday_min_1,
                'workday_min_2' => $this->shift->workday_min_2,
                'default_workday_threshold' => $this->shift->default_workday_threshold ?? 1.0, // Truyền threshold
            ]);
        }

        return $shiftLogicService->calcWorkday([
            'schedule_start_time' => $shiftRule->start_time,
            'schedule_end_time' => $shiftRule->end_time,
            'start_time' => $this->checkin?->format('H:i:s'),
            'end_time' => $this->checkout?->format('H:i:s'),
            'workday_min_1' => $shiftRule->workday_min_1,
            'workday_min_2' => $shiftRule->workday_min_2,
            'break_times' => $shiftRule->break_times,
            'default_workday_threshold' => $this->shift->default_workday_threshold ?? 1.0, // Truyền threshold
        ]);
    }

    public function calcWorkDayRealHours(): float
    {
        // Kiểm tra shift có tồn tại không
        if (!$this->shift) {
            return 0;
        }

        try {
            /** @var ShiftLogicTypeInterface $shiftLogicService */
            $shiftLogicService = $this->shift->getLogicTypeService();

            if (!$shiftLogicService) {
                \Log::error('ShiftLogicService is null for shift in calcWorkDayRealHours', [
                    'shift_id' => $this->shift->id,
                    'timesheet_id' => $this->id
                ]);
                return 0;
            }
        } catch (\Exception $e) {
            \Log::error('Error getting ShiftLogicService in calcWorkDayRealHours', [
                'shift_id' => $this->shift->id,
                'timesheet_id' => $this->id,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
        $shiftRule = $this->getShiftRule();

        if (!$shiftRule) {
            // Fallback nếu không tìm thấy quy tắc - không sử dụng hardcoded break times
            return $shiftLogicService->calcWorkdayRealHours([
                'schedule_start_time' => $this->shift->start_time,
                'schedule_end_time' => $this->shift->end_time,
                'start_time' => $this->checkin?->format('H:i:s'),
                'end_time' => $this->checkout?->format('H:i:s'),
                'break_times' => null, // No break times if not configured in shift rule
                'workday_min_1' => $this->shift->workday_min_1,
                'workday_min_2' => $this->shift->workday_min_2,
            ]);
        }

        return $shiftLogicService->calcWorkdayRealHours([
            'schedule_start_time' => $shiftRule->start_time,
            'schedule_end_time' => $shiftRule->end_time,
            'start_time' => $this->checkin?->format('H:i:s'),
            'end_time' => $this->checkout?->format('H:i:s'),
            'break_times' => $shiftRule->break_times,
            'workday_min_1' => $shiftRule->workday_min_1,
            'workday_min_2' => $shiftRule->workday_min_2,
        ]);
    }

    /**
     * Tính toán số công với logic mới: ưu tiên ca checkout, fallback ca checkin
     *
     * @return float
     */
    public function calcWorkdayWithShiftDetermination(): float
    {
        if (!$this->checkin || !$this->checkout) {
            return 0;
        }

        // Sử dụng ShiftDeterminationService để xác định ca phù hợp
        $shiftDeterminationService = app(\App\Services\Timesheet\ShiftDeterminationService::class);
        return $shiftDeterminationService->calculateWorkdayWithDeterminedShift($this);
    }

    /**
     * Lấy ca làm việc được xác định để tính công (ưu tiên checkout)
     *
     * @return \App\Models\Shift|null
     */
    public function getDeterminedWorkdayShift(): ?\App\Models\Shift
    {
        $shiftDeterminationService = app(\App\Services\Timesheet\ShiftDeterminationService::class);
        return $shiftDeterminationService->determineWorkdayShift($this);
    }
}
