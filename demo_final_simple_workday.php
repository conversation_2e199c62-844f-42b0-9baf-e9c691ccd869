<?php

/**
 * Demo cuối cùng cho Simple Workday Calculator
 * 
 * Chạy: php demo_final_simple_workday.php
 */

echo "=== DEMO: SIMPLE WORKDAY CALCULATOR (FINAL) ===\n\n";

// Mock timesheet data
function createTimesheet($checkin, $checkout) {
    return (object) [
        'checkin' => $checkin ? new DateTime($checkin) : null,
        'checkout' => $checkout ? new DateTime($checkout) : null,
    ];
}

// Simple calculator functions
function getEarliestCheckin($timesheets) {
    $earliest = null;
    foreach ($timesheets as $timesheet) {
        if ($timesheet->checkin && (!$earliest || $timesheet->checkin < $earliest)) {
            $earliest = $timesheet->checkin;
        }
    }
    return $earliest;
}

function getSmartCheckout($timesheets) {
    // Tìm checkout cuối cùng
    $latestCheckout = null;
    $latestCheckoutTime = null;
    
    // Tìm checkin cuối cùng
    $latestCheckin = null;
    $latestCheckinTime = null;
    
    foreach ($timesheets as $timesheet) {
        if ($timesheet->checkout && (!$latestCheckoutTime || $timesheet->checkout > $latestCheckoutTime)) {
            $latestCheckout = $timesheet->checkout;
            $latestCheckoutTime = $timesheet->checkout;
        }
        
        if ($timesheet->checkin && (!$latestCheckinTime || $timesheet->checkin > $latestCheckinTime)) {
            $latestCheckin = $timesheet->checkin;
            $latestCheckinTime = $timesheet->checkin;
        }
    }
    
    // Logic: So sánh và lấy thời gian muộn nhất
    if ($latestCheckoutTime && $latestCheckinTime) {
        return $latestCheckoutTime > $latestCheckinTime ? $latestCheckout : $latestCheckin;
    }
    
    // Nếu chỉ có một trong hai
    return $latestCheckout ?: $latestCheckin;
}

function calculateSimpleWorkday($timesheets) {
    if (empty($timesheets)) {
        return ['checkin' => null, 'checkout' => null, 'hours' => 0, 'method' => 'empty'];
    }
    
    if (count($timesheets) === 1) {
        $timesheet = $timesheets[0];
        $hours = 0;
        if ($timesheet->checkin && $timesheet->checkout) {
            $diff = $timesheet->checkout->diff($timesheet->checkin);
            $hours = $diff->h + ($diff->i / 60);
        }
        return [
            'checkin' => $timesheet->checkin,
            'checkout' => $timesheet->checkout,
            'hours' => $hours,
            'method' => 'single'
        ];
    }
    
    // Multiple timesheets - áp dụng logic đơn giản
    $smartCheckin = getEarliestCheckin($timesheets);
    $smartCheckout = getSmartCheckout($timesheets);
    
    $hours = 0;
    if ($smartCheckin && $smartCheckout) {
        $diff = $smartCheckout->diff($smartCheckin);
        $hours = $diff->h + ($diff->i / 60);
    }
    
    return [
        'checkin' => $smartCheckin,
        'checkout' => $smartCheckout,
        'hours' => $hours,
        'method' => 'multiple_smart'
    ];
}

// Test cases
$testCases = [
    [
        'name' => '✅ Multiple timesheets đầy đủ',
        'timesheets' => [
            createTimesheet('2025-06-01 08:30:00', '2025-06-01 12:00:00'),
            createTimesheet('2025-06-01 13:00:00', '2025-06-01 17:30:00'),
        ],
        'expected' => 'Checkin: 08:30 → Checkout: 17:30 (9 giờ)',
    ],
    [
        'name' => '🎯 Bản ghi cuối chỉ có checkin',
        'timesheets' => [
            createTimesheet('2025-06-01 08:30:00', '2025-06-01 12:00:00'),
            createTimesheet('2025-06-01 13:00:00', null), // Chỉ có checkin
        ],
        'expected' => 'Checkin: 08:30 → Checkout: 13:00 (4.5 giờ)',
    ],
    [
        'name' => '⚡ Multiple checkin/checkout không đều',
        'timesheets' => [
            createTimesheet('2025-06-01 08:15:00', null), // Chỉ checkin
            createTimesheet('2025-06-01 09:00:00', '2025-06-01 12:00:00'),
            createTimesheet('2025-06-01 13:30:00', '2025-06-01 18:00:00'),
        ],
        'expected' => 'Checkin: 08:15 → Checkout: 18:00 (9h45)',
    ],
    [
        'name' => '🔄 Tất cả chỉ có checkin',
        'timesheets' => [
            createTimesheet('2025-06-01 08:30:00', null),
            createTimesheet('2025-06-01 12:00:00', null),
            createTimesheet('2025-06-01 17:30:00', null),
        ],
        'expected' => 'Checkin: 08:30 → Checkout: 17:30 (9 giờ)',
    ],
    [
        'name' => '📝 Single timesheet',
        'timesheets' => [
            createTimesheet('2025-06-01 08:30:00', '2025-06-01 17:30:00'),
        ],
        'expected' => 'Checkin: 08:30 → Checkout: 17:30 (9 giờ)',
    ],
];

// Chạy test cases
foreach ($testCases as $i => $testCase) {
    echo "--- {$testCase['name']} ---\n";
    
    // Hiển thị input
    echo "Input timesheets:\n";
    foreach ($testCase['timesheets'] as $j => $timesheet) {
        $checkinStr = $timesheet->checkin ? $timesheet->checkin->format('H:i') : 'N/A';
        $checkoutStr = $timesheet->checkout ? $timesheet->checkout->format('H:i') : 'N/A';
        echo "  " . ($j + 1) . ". Checkin: {$checkinStr}, Checkout: {$checkoutStr}\n";
    }
    
    // Tính toán
    $result = calculateSimpleWorkday($testCase['timesheets']);
    
    // Hiển thị kết quả
    $checkinResult = $result['checkin'] ? $result['checkin']->format('H:i') : 'N/A';
    $checkoutResult = $result['checkout'] ? $result['checkout']->format('H:i') : 'N/A';
    $hoursResult = number_format($result['hours'], 1);
    
    echo "Kết quả:\n";
    echo "  ⏰ Smart Checkin: {$checkinResult}\n";
    echo "  ⏰ Smart Checkout: {$checkoutResult}\n";
    echo "  📊 Tổng giờ làm: {$hoursResult} giờ\n";
    echo "  🔧 Method: {$result['method']}\n";
    echo "Mong đợi: {$testCase['expected']}\n\n";
}

echo "=== 🎯 TỔNG KẾT LOGIC ĐƠN GIẢN ===\n";
echo "📋 Quy tắc:\n";
echo "1. 🕐 Lấy CHECKIN SỚM NHẤT từ tất cả timesheets\n";
echo "2. 🕐 So sánh CHECKOUT CUỐI vs CHECKIN CUỐI → lấy thời gian MUỘN NHẤT\n";
echo "3. ⏱️  Tính workday = Smart Checkin → Smart Checkout\n\n";

echo "✅ Lợi ích:\n";
echo "- 🎯 Logic đơn giản, dễ hiểu và implement\n";
echo "- 🔧 Xử lý được trường hợp bản ghi cuối chỉ có checkin\n";
echo "- 📊 Tính toán chính xác tổng thời gian làm việc\n";
echo "- 🚀 Không cần phức tạp hóa với nhiều ca\n";
echo "- 💡 Phù hợp với yêu cầu thực tế của user\n\n";

echo "🎯 Ví dụ thực tế:\n";
echo "👤 Nhân viên checkin 08:30, ra ăn trưa 12:00, quên checkout\n";
echo "👤 Sau đó checkin lại 13:00 để làm tiếp\n";
echo "💻 Hệ thống tính: 08:30 - 13:00 = 4.5 giờ làm việc\n\n";

echo "🚀 Để áp dụng trong production:\n";
echo "1. Thêm vào .env: TIMESHEET_USE_SIMPLE_CALCULATOR=true\n";
echo "2. Service sẽ tự động sử dụng logic mới\n";
echo "3. Có thể test trước khi enable toàn bộ\n\n";

echo "✨ HOÀN THÀNH! Logic đã sẵn sàng để sử dụng.\n";

?>
