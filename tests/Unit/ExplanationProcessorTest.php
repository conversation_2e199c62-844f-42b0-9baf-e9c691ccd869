<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;
use App\Services\AttendanceExplanation\ExplanationProcessorFactory;
use App\Services\AttendanceExplanation\Processors\LateExplanationProcessor;
use App\Services\AttendanceExplanation\Processors\OvertimeExplanationProcessor;
use App\Services\AttendanceExplanation\Processors\RemoteWorkExplanationProcessor;
use Illuminate\Support\Collection;

class ExplanationProcessorTest extends TestCase
{
    public function test_factory_creates_correct_processors()
    {
        // Test Late Explanation
        $processor = ExplanationProcessorFactory::create('late');
        $this->assertInstanceOf(LateExplanationProcessor::class, $processor);

        // Test Overtime Explanation
        $processor = ExplanationProcessorFactory::create('overtime');
        $this->assertInstanceOf(OvertimeExplanationProcessor::class, $processor);

        // Test Remote Work Explanation
        $processor = ExplanationProcessorFactory::create('remote_work');
        $this->assertInstanceOf(RemoteWorkExplanationProcessor::class, $processor);
    }

    public function test_factory_throws_exception_for_unknown_type()
    {
        $this->expectException(\InvalidArgumentException::class);
        ExplanationProcessorFactory::create('unknown_type');
    }

    public function test_overtime_explanation_only_row()
    {
        $explanation = (object) [
            'explanation_type' => 'overtime',
            'ot_hours' => 2.5,
            'explanation' => 'Làm thêm giờ dự án'
        ];

        $result = ExplanationProcessorFactory::processExplanationOnlyRow($explanation);

        $this->assertEquals(0, $result['workday']); // OT không tính công
        $this->assertEquals(2.5, $result['overtime_hours']);
        $this->assertEquals('Chỉ OT', $result['attendance_status']);
    }

    public function test_remote_work_explanation_only_row()
    {
        $explanation = (object) [
            'explanation_type' => 'remote_work',
            'remote_shift_id' => null,
            'explanation' => 'Làm việc từ xa'
        ];

        $result = ExplanationProcessorFactory::processExplanationOnlyRow($explanation);

        $this->assertEquals(1.0, $result['workday']); // Default workday
        $this->assertEquals('Làm việc từ xa', $result['attendance_status']);
    }

    public function test_late_explanation_only_row()
    {
        $explanation = (object) [
            'explanation_type' => 'late',
            'explanation' => 'Đi muộn do kẹt xe'
        ];

        $result = ExplanationProcessorFactory::processExplanationOnlyRow($explanation);

        $this->assertEquals(1.0, $result['workday']); // Giả định ca HC
        $this->assertEquals('Có giải trình', $result['attendance_status']);
    }

    public function test_apply_all_explanations()
    {
        $explanations = collect([
            (object) [
                'explanation_type' => 'overtime',
                'ot_hours' => 1.5,
                'explanation' => 'OT dự án A',
                'final_status' => 'approved'
            ],
            (object) [
                'explanation_type' => 'late',
                'explanation' => 'Đi muộn',
                'final_status' => 'approved'
            ]
        ]);

        $baseData = [
            'attendance_status' => 'normal',
            'explanation_summary' => '',
            'final_workday' => 1.0,
            'overtime_hours' => 0,
        ];

        $result = ExplanationProcessorFactory::applyAllExplanations($baseData, $explanations);

        $this->assertEquals(1.5, $result['overtime_hours']);
        $this->assertNotEmpty($result['explanation_summary']);
    }

    public function test_supported_types()
    {
        $supportedTypes = ExplanationProcessorFactory::getSupportedTypes();
        
        $expectedTypes = [
            'late', 'early', 'insufficient_hours', 'no_checkin', 'no_checkout',
            'overtime', 'remote_work', 'shift_change', 'new_employee_no_account', 'other'
        ];

        foreach ($expectedTypes as $type) {
            $this->assertContains($type, $supportedTypes);
        }
    }

    public function test_is_supported()
    {
        $this->assertTrue(ExplanationProcessorFactory::isSupported('late'));
        $this->assertTrue(ExplanationProcessorFactory::isSupported('overtime'));
        $this->assertFalse(ExplanationProcessorFactory::isSupported('unknown_type'));
    }
}
