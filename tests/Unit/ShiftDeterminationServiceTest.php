<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;
use App\Services\Timesheet\ShiftDeterminationService;
use App\Models\Timesheet;
use App\Models\Shift;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class ShiftDeterminationServiceTest extends TestCase
{
    private ShiftDeterminationService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new ShiftDeterminationService();
    }

    public function test_determine_shift_for_single_timesheet_with_checkout()
    {
        // Mock user với 2 ca làm việc
        $user = $this->createMockUser();
        $morningShift = $this->createMockShift(1, 'Morning', '08:00:00', '17:00:00');
        $eveningShift = $this->createMockShift(2, 'Evening', '18:00:00', '02:00:00');
        
        $user->shifts = collect([$morningShift, $eveningShift]);

        // Timesheet với checkout lúc 17:30 (gần ca sáng hơn)
        $timesheet = $this->createMockTimesheet($user, '2025-06-01 08:30:00', '2025-06-01 17:30:00');

        $result = $this->service->determineWorkdayShift($timesheet);

        // Kỳ vọng chọn ca sáng vì checkout time gần ca sáng hơn
        $this->assertEquals($morningShift->id, $result->id);
    }

    public function test_determine_shift_for_single_timesheet_without_checkout()
    {
        // Mock user với 2 ca làm việc
        $user = $this->createMockUser();
        $morningShift = $this->createMockShift(1, 'Morning', '08:00:00', '17:00:00');
        $eveningShift = $this->createMockShift(2, 'Evening', '18:00:00', '02:00:00');
        
        $user->shifts = collect([$morningShift, $eveningShift]);

        // Timesheet chỉ có checkin lúc 18:15 (gần ca tối hơn)
        $timesheet = $this->createMockTimesheet($user, '2025-06-01 18:15:00', null);

        $result = $this->service->determineWorkdayShift($timesheet);

        // Kỳ vọng chọn ca tối vì checkin time gần ca tối hơn
        $this->assertEquals($eveningShift->id, $result->id);
    }

    public function test_determine_shift_for_multiple_timesheets()
    {
        // Mock user với 2 ca làm việc
        $user = $this->createMockUser();
        $morningShift = $this->createMockShift(1, 'Morning', '08:00:00', '17:00:00');
        $eveningShift = $this->createMockShift(2, 'Evening', '18:00:00', '02:00:00');
        
        $user->shifts = collect([$morningShift, $eveningShift]);

        // Multiple timesheets với checkout cuối cùng lúc 01:30 (ca tối)
        $timesheets = collect([
            $this->createMockTimesheet($user, '2025-06-01 08:30:00', '2025-06-01 12:00:00'),
            $this->createMockTimesheet($user, '2025-06-01 18:00:00', '2025-06-02 01:30:00'), // checkout cuối
        ]);

        $result = $this->service->determineWorkdayShift($timesheets);

        // Kỳ vọng chọn ca tối vì checkout cuối cùng thuộc ca tối
        $this->assertEquals($eveningShift->id, $result->id);
    }

    public function test_calculate_time_distance()
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('calculateTimeDistance');
        $method->setAccessible(true);

        // Test thời gian nằm trong ca (distance = 0)
        $distance = $method->invoke($this->service, '09:00:00', '08:00:00', '17:00:00');
        $this->assertEquals(0, $distance);

        // Test thời gian trước ca (distance = 60 phút)
        $distance = $method->invoke($this->service, '07:00:00', '08:00:00', '17:00:00');
        $this->assertEquals(60, $distance);

        // Test thời gian sau ca (distance = 60 phút)
        $distance = $method->invoke($this->service, '18:00:00', '08:00:00', '17:00:00');
        $this->assertEquals(60, $distance);
    }

    private function createMockUser(): User
    {
        $user = $this->createMock(User::class);
        $user->id = 1;
        $user->name = 'Test User';
        return $user;
    }

    private function createMockShift(int $id, string $name, string $startTime, string $endTime): Shift
    {
        $shift = $this->createMock(Shift::class);
        $shift->id = $id;
        $shift->name = $name;
        $shift->start_time = $startTime;
        $shift->end_time = $endTime;
        $shift->default_workday_threshold = 1.0;
        
        // Mock getActiveRule method
        $shift->method('getActiveRule')->willReturn(null);
        
        return $shift;
    }

    private function createMockTimesheet(User $user, ?string $checkin, ?string $checkout): Timesheet
    {
        $timesheet = $this->createMock(Timesheet::class);
        $timesheet->user = $user;
        $timesheet->date = '2025-06-01';
        $timesheet->checkin = $checkin ? Carbon::parse($checkin) : null;
        $timesheet->checkout = $checkout ? Carbon::parse($checkout) : null;
        
        return $timesheet;
    }
}
