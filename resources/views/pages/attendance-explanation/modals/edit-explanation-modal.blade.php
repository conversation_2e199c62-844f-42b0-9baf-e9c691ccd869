<!-- Modal sửa giải trình -->
<div class="modal fade" id="editExplanationModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Cập nhật gi<PERSON>i trình chấm công</h4>
            </div>
            <form id="editExplanationForm" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <input type="hidden" id="edit_explanation_id" name="explanation_id">

                    <div class="form-group">
                        <label>Ngày:</label>
                        <input type="text" class="form-control" id="edit_selected_date" readonly>
                    </div>

                    <div class="form-group">
                        <label for="edit_explanation_type">Loại giải trình:</label>
                        <select class="form-control" id="edit_explanation_type" name="explanation_type" required>
                            <option value="">-- Chọn loại giải trình --</option>
                            <option value="late">Đi muộn</option>
                            <option value="early">Về sớm</option>
                            <option value="insufficient_hours">Thiếu giờ làm việc</option>
                            <option value="no_checkin">Quên checkin</option>
                            <option value="no_checkout">Quên checkout</option>
                            <option value="overtime">Làm thêm giờ (OT)</option>
                            <option value="remote_work">Làm việc remote</option>
                            <option value="shift_change">Thay đổi ca làm việc</option>
                            <option value="new_employee_no_account">Người mới đi làm chưa được cấp tài khoản</option>
                            <option value="other">Khác</option>
                        </select>
                    </div>

                    <!-- OT Hours input (ẩn mặc định) -->
                    <div class="form-group" id="edit_ot_hours_group" style="display: none;">
                        <label for="edit_ot_hours">Số giờ OT:</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="edit_ot_hours" name="ot_hours"
                                   min="0.1" max="24" step="0.1" placeholder="Ví dụ: 2.5">
                            <div class="input-group-addon">giờ</div>
                        </div>
                    </div>

                    <!-- Remote Shift input (ẩn mặc định) -->
                    <div class="form-group" id="edit_remote_shift_group" style="display: none;">
                        <label for="edit_remote_shift_id">Ca làm việc remote:</label>
                        <select class="form-control" id="edit_remote_shift_id" name="remote_shift_id">
                            <option value="">-- Chọn ca làm việc --</option>
                            @foreach(auth()->user()->getAvailableShifts() as $shift)
                                <option value="{{ $shift->id }}">{{ $shift->name }} ({{ $shift->start_time }} - {{ $shift->end_time }})</option>
                            @endforeach
                        </select>
                        <small class="text-muted">Chỉ hiển thị ca làm việc mà bạn được phép sử dụng</small>
                    </div>

                    <!-- Shift Change input (ẩn mặc định) -->
                    <div class="form-group" id="edit_shift_change_group" style="display: none;">
                        <label for="edit_shift_change_id">Ca làm việc mới:</label>
                        <select class="form-control" id="edit_shift_change_id" name="shift_change_id">
                            <option value="">-- Chọn ca làm việc mới --</option>
                            <!-- Shifts sẽ được load động bằng JavaScript dựa trên ngày được chọn -->
                        </select>
                        <small class="text-muted">Chọn ca làm việc mới để thay đổi</small>

                        <!-- Warning message for shift mismatch -->
                        <div id="edit_shift_warning" class="alert alert-warning" style="display: none; margin-top: 10px;">
                            <i class="fa fa-exclamation-triangle"></i>
                            <strong>Cảnh báo:</strong> <span id="edit_shift_warning_message"></span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Quy trình duyệt (2 bước):</label>
                        <div class="form-control-static">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fa fa-user text-primary"></i> Bước 1: Manager</h6>
                                    @if($defaultManager)
                                        <span class="text-success">{{ $defaultManager->name }}</span>
                                        @if($defaultManager->phone_number)
                                            <small class="text-muted">({{ substr($defaultManager->phone_number, -4) }})</small>
                                        @endif
                                    @else
                                        <span class="text-muted">Chưa có người quản lý</span>
                                    @endif
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fa fa-building text-info"></i> Bước 2: HR</h6>
                                    <span class="text-success">Hành chính nhân sự</span>
                                    <br><small class="text-muted">Duyệt sau khi manager đã duyệt</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- @if($colleagues->count() > 0)
                    <div class="form-group">
                        <label>Tag 1 người xác nhận: <small class="text-muted">(Tùy chọn)</small></label>
                        <select class="form-control select2" id="edit_tagged_user_id" name="tagged_user_id" data-placeholder="Chọn 1 đồng nghiệp để xác nhận..." style="width: 100%;">
                            <option value="">-- Không tag ai --</option>
                            @foreach($colleagues as $colleague)
                                <option value="{{ $colleague->id }}">
                                    {{ $colleague->name }}
                                    @if($colleague->account)
                                        - {{ $colleague->account }}
                                    @endif
                                    @if($colleague->staffDepartment)
                                        - {{ $colleague->staffDepartment->name }}
                                    @endif
                                    @if($colleague->phone_number)
                                        - {{ substr($colleague->phone_number, -4) }}
                                    @endif
                                </option>
                            @endforeach
                        </select>
                        <small class="text-muted">Chọn 1 đồng nghiệp để xác nhận giải trình trước khi gửi manager duyệt</small>
                    </div>
                    @endif -->

                    <div class="form-group">
                        <label>Lý do giải trình:</label>
                        <textarea class="form-control" id="edit_explanation" name="explanation"
                                  rows="4" maxlength="500" required
                                  placeholder="Vui lòng mô tả chi tiết lý do..."></textarea>
                        <small class="text-muted">Tối đa 500 ký tự</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-primary">Cập nhật giải trình</button>
                </div>
            </form>
        </div>
    </div>
</div>
