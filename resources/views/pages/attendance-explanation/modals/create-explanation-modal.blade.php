<!-- Modal giải trình -->
<div class="modal fade" id="explanationModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Giải trình chấm công</h4>
            </div>
            <form id="explanationForm">
                <div class="modal-body">
                    <input type="hidden" id="explanation_date" name="date">

                    <div class="form-group">
                        <label>Ngày:</label>
                        <p id="selected_date" class="form-control-static"></p>
                    </div>

                    <div class="form-group">
                        <label>Loại giải trình:</label>
                        <select class="form-control" id="explanation_type" name="explanation_type" required>
                            <option value="">-- Ch<PERSON>n loại giải trình --</option>
                            <option value="late">Đi muộn</option>
                            <option value="early">Về sớm</option>
                            <option value="insufficient_hours">Thiếu giờ làm việc</option>
                            <option value="no_checkin">Quên checkin</option>
                            <option value="no_checkout">Quên checkout</option>
                            <option value="overtime">Làm thêm giờ (OT)</option>
                            <option value="remote_work">Làm việc remote</option>
                            <option value="shift_change">Thay đổi ca làm việc</option>
                            <option value="new_employee_no_account">Người mới đi làm chưa được cấp tài khoản</option>
                            <option value="other">Khác</option>
                        </select>
                    </div>

                    <!-- Input số giờ OT - chỉ hiển thị khi chọn overtime -->
                    <div class="form-group" id="ot_hours_group" style="display: none;">
                        <label>Số giờ OT: <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="ot_hours" name="ot_hours"
                               step="0.1" min="0.1" max="24" placeholder="Nhập số giờ OT (VD: 2.5)">
                        <small class="text-muted">Nhập số giờ làm thêm (từ 0.1 đến 24 giờ)</small>
                    </div>

                    <!-- Input ca làm việc - chỉ hiển thị khi chọn remote work -->
                    <div class="form-group" id="remote_shift_group" style="display: none;">
                        <label>Ca làm việc remote: <span class="text-danger">*</span></label>
                        <select class="form-control" id="remote_shift_id" name="remote_shift_id">
                            <option value="">-- Chọn ca làm việc --</option>
                            @foreach(auth()->user()->getAvailableShifts() as $shift)
                                <option value="{{ $shift->id }}">{{ $shift->name }} ({{ $shift->start_time }} - {{ $shift->end_time }})</option>
                            @endforeach
                        </select>
                        <small class="text-muted">Chỉ hiển thị ca làm việc mà bạn được phép sử dụng</small>
                    </div>

                    <!-- Input ca làm việc mới - chỉ hiển thị khi chọn shift change -->
                    <div class="form-group" id="shift_change_group" style="display: none;">
                        <label>Ca làm việc mới: <span class="text-danger">*</span></label>
                        <select class="form-control" id="shift_change_id" name="shift_change_id">
                            <option value="">-- Chọn ca làm việc mới --</option>
                            <!-- Fallback shifts (sẽ được thay thế bằng JavaScript nếu API hoạt động) -->
                            @foreach(auth()->user()->getAvailableShifts() as $shift)
                                <option value="{{ $shift->id }}" data-start-time="{{ $shift->start_time }}" data-end-time="{{ $shift->end_time }}">{{ $shift->name }} ({{ $shift->start_time }} - {{ $shift->end_time }})</option>
                            @endforeach
                        </select>
                        <small class="text-muted">Chọn ca làm việc mới để thay đổi</small>

                        <!-- Warning message for shift mismatch -->
                        <div id="shift_warning" class="alert alert-warning" style="display: none; margin-top: 10px;">
                            <i class="fa fa-exclamation-triangle"></i>
                            <strong>Cảnh báo:</strong> <span id="shift_warning_message"></span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Quy trình duyệt (2 bước):</label>
                        <div class="form-control-static">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fa fa-user text-primary"></i> Bước 1: Manager</h6>
                                    @if($defaultManager)
                                        <span class="text-success">{{ $defaultManager->name }}</span>
                                        @if($defaultManager->phone_number)
                                            <small class="text-muted">({{ substr($defaultManager->phone_number, -4) }})</small>
                                        @endif
                                    @else
                                        <span class="text-muted">Chưa có người quản lý</span>
                                    @endif
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fa fa-building text-info"></i> Bước 2: HR</h6>
                                    <span class="text-success">Hành chính nhân sự</span>
                                    <br><small class="text-muted">Duyệt sau khi manager đã duyệt</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- @if($colleagues->count() > 0)
                    <div class="form-group">
                        <label>Tag 1 người xác nhận: <small class="text-muted">(Tùy chọn)</small></label>
                        <select class="form-control select2" id="tagged_user_id" name="tagged_user_id" data-placeholder="Chọn 1 đồng nghiệp để xác nhận..." style="width: 100%;">
                            <option value="">-- Không tag ai --</option>
                            @foreach($colleagues as $colleague)
                                <option value="{{ $colleague->id }}">
                                    {{ $colleague->name }}
                                    @if($colleague->account)
                                        - {{ $colleague->account }}
                                    @endif
                                    @if($colleague->staffDepartment)
                                        - {{ $colleague->staffDepartment->name }}
                                    @endif
                                    @if($colleague->phone_number)
                                        - {{ substr($colleague->phone_number, -4) }}
                                    @endif
                                </option>
                            @endforeach
                        </select>
                        <small class="text-muted">Chọn 1 đồng nghiệp để xác nhận giải trình trước khi gửi manager duyệt</small>
                    </div>
                    @endif -->

                    <div class="form-group">
                        <label>Lý do giải trình:</label>
                        <textarea class="form-control" id="explanation" name="explanation"
                                  rows="4" maxlength="500" required
                                  placeholder="Vui lòng mô tả chi tiết lý do..."></textarea>
                        <small class="text-muted">Tối đa 500 ký tự</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-primary">Lưu giải trình</button>
                </div>
            </form>
        </div>
    </div>
</div>
