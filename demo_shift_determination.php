<?php

/**
 * Demo script để minh họa logic Shift Determination
 * 
 * Chạy: php demo_shift_determination.php
 */

echo "=== DEMO: SHIFT DETERMINATION LOGIC ===\n\n";

// Mock data
$userShifts = [
    ['id' => 1, 'name' => 'Ca sáng', 'start_time' => '08:00:00', 'end_time' => '17:00:00'],
    ['id' => 2, 'name' => 'Ca tối', 'start_time' => '18:00:00', 'end_time' => '02:00:00'],
    ['id' => 3, 'name' => 'Ca HC', 'start_time' => '08:30:00', 'end_time' => '17:30:00'],
];

$testCases = [
    [
        'name' => 'Trường hợp 1: Có checkout',
        'checkin' => '08:15:00',
        'checkout' => '17:45:00',
        'expected' => '<PERSON><PERSON> sáng (vì checkout 17:45 gần ca sáng hơn)',
    ],
    [
        'name' => 'Trường hợp 2: Checkout muộn',
        'checkin' => '08:30:00', 
        'checkout' => '01:30:00', // Ngày hôm sau
        'expected' => 'Ca tối (vì checkout 01:30 thuộc ca tối)',
    ],
    [
        'name' => 'Trường hợp 3: Chỉ có checkin',
        'checkin' => '18:15:00',
        'checkout' => null,
        'expected' => 'Ca tối (vì checkin 18:15 gần ca tối hơn)',
    ],
    [
        'name' => 'Trường hợp 4: Checkin sớm',
        'checkin' => '07:45:00',
        'checkout' => null,
        'expected' => 'Ca sáng (vì checkin 07:45 gần ca sáng hơn)',
    ],
];

function timeToMinutes($time) {
    $parts = explode(':', $time);
    return intval($parts[0]) * 60 + intval($parts[1]);
}

function calculateTimeDistance($time, $shiftStart, $shiftEnd) {
    $timeMinutes = timeToMinutes($time);
    $startMinutes = timeToMinutes($shiftStart);
    $endMinutes = timeToMinutes($shiftEnd);
    
    // Xử lý ca qua đêm (end_time < start_time)
    if ($endMinutes < $startMinutes) {
        // Ca qua đêm: 18:00 - 02:00
        if ($timeMinutes >= $startMinutes || $timeMinutes <= $endMinutes) {
            return 0; // Nằm trong ca
        }
        
        // Tính khoảng cách đến điểm gần nhất
        $distanceToStart = abs($timeMinutes - $startMinutes);
        $distanceToEnd = abs($timeMinutes - $endMinutes);
        
        // Xử lý đặc biệt cho ca qua đêm
        if ($timeMinutes > $endMinutes && $timeMinutes < $startMinutes) {
            // Thời gian nằm giữa end và start (ví dụ: 10:00 nằm giữa 02:00 và 18:00)
            $distanceToEnd = min($timeMinutes - $endMinutes, $endMinutes + (24 * 60) - $timeMinutes);
            $distanceToStart = min($startMinutes - $timeMinutes, $timeMinutes + (24 * 60) - $startMinutes);
        }
        
        return min($distanceToStart, $distanceToEnd);
    }
    
    // Ca bình thường
    if ($timeMinutes >= $startMinutes && $timeMinutes <= $endMinutes) {
        return 0; // Nằm trong ca
    }
    
    $distanceToStart = abs($timeMinutes - $startMinutes);
    $distanceToEnd = abs($timeMinutes - $endMinutes);
    
    return min($distanceToStart, $distanceToEnd);
}

function findBestShift($time, $shifts) {
    if (!$time) return null;
    
    $bestShift = null;
    $minDistance = PHP_INT_MAX;
    
    foreach ($shifts as $shift) {
        $distance = calculateTimeDistance($time, $shift['start_time'], $shift['end_time']);
        
        if ($distance < $minDistance) {
            $minDistance = $distance;
            $bestShift = $shift;
        }
    }
    
    return $bestShift;
}

function determineShift($checkin, $checkout, $shifts) {
    // Ưu tiên checkout nếu có
    if ($checkout) {
        return findBestShift($checkout, $shifts);
    }
    
    // Fallback checkin
    if ($checkin) {
        return findBestShift($checkin, $shifts);
    }
    
    return null;
}

// Chạy test cases
foreach ($testCases as $i => $testCase) {
    echo "--- {$testCase['name']} ---\n";
    echo "Checkin: " . ($testCase['checkin'] ?? 'N/A') . "\n";
    echo "Checkout: " . ($testCase['checkout'] ?? 'N/A') . "\n";
    
    $determinedShift = determineShift($testCase['checkin'], $testCase['checkout'], $userShifts);
    
    echo "Kết quả: " . ($determinedShift ? $determinedShift['name'] : 'Không xác định được') . "\n";
    echo "Mong đợi: {$testCase['expected']}\n";
    
    // Hiển thị chi tiết tính toán
    $timeToCheck = $testCase['checkout'] ?? $testCase['checkin'];
    if ($timeToCheck) {
        echo "Chi tiết tính toán cho thời gian {$timeToCheck}:\n";
        foreach ($userShifts as $shift) {
            $distance = calculateTimeDistance($timeToCheck, $shift['start_time'], $shift['end_time']);
            echo "  - {$shift['name']} ({$shift['start_time']}-{$shift['end_time']}): khoảng cách = {$distance} phút\n";
        }
    }
    
    echo "\n";
}

echo "=== TỔNG KẾT ===\n";
echo "Logic Shift Determination:\n";
echo "1. Ưu tiên sử dụng thời gian checkout để xác định ca\n";
echo "2. Nếu không có checkout, sử dụng thời gian checkin\n";
echo "3. Tìm ca có khoảng cách thời gian ngắn nhất\n";
echo "4. Hỗ trợ ca qua đêm (ví dụ: 18:00-02:00)\n";
echo "5. Nếu thời gian nằm trong ca, khoảng cách = 0\n\n";

echo "Lợi ích:\n";
echo "✅ Tính toán chính xác hơn khi nhân viên làm nhiều ca\n";
echo "✅ Xử lý được trường hợp checkout muộn hoặc qua đêm\n";
echo "✅ Linh hoạt với các loại ca làm việc khác nhau\n";
echo "✅ Fallback an toàn khi thiếu dữ liệu\n";

?>
