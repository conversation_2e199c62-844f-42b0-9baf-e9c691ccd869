<?php

/**
 * Test script cho AttendanceViolationChecker service
 * 
 * Chạy: php test_violation_checker.php
 */

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Services\AttendanceExplanation\AttendanceViolationChecker;
use App\Services\AttendanceExplanation\AttendanceStatusCalculator;

echo "=== TEST: ATTENDANCE VIOLATION CHECKER ===\n\n";

// Mock AttendanceStatusCalculator
class MockAttendanceStatusCalculator {
    public function determineAttendanceStatus($workday, $lateMinutes, $earlyMinutes, $timesheet) {
        if ($lateMinutes > 0 || $earlyMinutes > 0) {
            return 'has_issue';
        }
        if ($workday >= 1.0) {
            return 'perfect';
        }
        return 'insufficient_hours';
    }
    
    public function getStatusText($status) {
        $statusTexts = [
            'perfect' => 'Hoàn hảo',
            'has_issue' => 'Có vấn đề',
            'insufficient_hours' => 'Thiếu giờ',
            'no_data' => 'Chưa chấm công'
        ];
        return $statusTexts[$status] ?? 'Không xác định';
    }
    
    public function getStatusColor($status) {
        $statusColors = [
            'perfect' => 'success',
            'has_issue' => 'danger',
            'insufficient_hours' => 'warning',
            'no_data' => 'default'
        ];
        return $statusColors[$status] ?? 'default';
    }
}

// Khởi tạo service với real class
$statusCalculator = app(AttendanceStatusCalculator::class);
$violationChecker = new AttendanceViolationChecker($statusCalculator);

// Test cases
$testCases = [
    'Case 1: Hoàn hảo - Không vi phạm' => [
        'consolidated_data' => [
            'date' => \Carbon\Carbon::parse('2025-06-28'),
            'checkin' => \Carbon\Carbon::parse('2025-06-28 08:00:00'),
            'checkout' => \Carbon\Carbon::parse('2025-06-28 17:00:00'),
            'working_shift' => (object) [
                'id' => 1,
                'name' => 'Ca sáng',
                'start_time' => '08:00:00',
                'end_time' => '17:00:00'
            ],
            'workday' => 1.0,
            'checkout_source' => 'last_timesheet_checkout'
        ],
        'explanations' => collect([])
    ],
    
    'Case 2: Đi muộn 30 phút' => [
        'consolidated_data' => [
            'date' => \Carbon\Carbon::parse('2025-06-28'),
            'checkin' => \Carbon\Carbon::parse('2025-06-28 08:30:00'),
            'checkout' => \Carbon\Carbon::parse('2025-06-28 17:00:00'),
            'working_shift' => (object) [
                'id' => 1,
                'name' => 'Ca sáng',
                'start_time' => '08:00:00',
                'end_time' => '17:00:00'
            ],
            'workday' => 1.0,
            'checkout_source' => 'last_timesheet_checkout'
        ],
        'explanations' => collect([])
    ],
    
    'Case 3: Về sớm 45 phút' => [
        'consolidated_data' => [
            'date' => \Carbon\Carbon::parse('2025-06-28'),
            'checkin' => \Carbon\Carbon::parse('2025-06-28 08:00:00'),
            'checkout' => \Carbon\Carbon::parse('2025-06-28 16:15:00'),
            'working_shift' => (object) [
                'id' => 1,
                'name' => 'Ca sáng',
                'start_time' => '08:00:00',
                'end_time' => '17:00:00'
            ],
            'workday' => 0.8,
            'checkout_source' => 'last_timesheet_checkout'
        ],
        'explanations' => collect([])
    ],
    
    'Case 4: Thiếu checkout (dùng checkin làm checkout)' => [
        'consolidated_data' => [
            'date' => \Carbon\Carbon::parse('2025-06-28'),
            'checkin' => \Carbon\Carbon::parse('2025-06-28 08:30:00'),
            'checkout' => \Carbon\Carbon::parse('2025-06-28 18:30:00'), // Checkin làm checkout
            'working_shift' => (object) [
                'id' => 1,
                'name' => 'Ca sáng',
                'start_time' => '08:00:00',
                'end_time' => '17:00:00'
            ],
            'workday' => 0.5,
            'checkout_source' => 'last_timesheet_checkin_as_checkout'
        ],
        'explanations' => collect([])
    ],
    
    'Case 5: Có giải trình được duyệt' => [
        'consolidated_data' => [
            'date' => \Carbon\Carbon::parse('2025-06-28'),
            'checkin' => \Carbon\Carbon::parse('2025-06-28 08:30:00'),
            'checkout' => \Carbon\Carbon::parse('2025-06-28 17:00:00'),
            'working_shift' => (object) [
                'id' => 1,
                'name' => 'Ca sáng',
                'start_time' => '08:00:00',
                'end_time' => '17:00:00'
            ],
            'workday' => 1.0,
            'checkout_source' => 'last_timesheet_checkout'
        ],
        'explanations' => collect([
            (object) [
                'id' => 1,
                'explanation_type' => 'late',
                'explanation' => 'Tắc đường',
                'final_status' => 'approved'
            ]
        ])
    ]
];

// Test function
function testViolationChecker($violationChecker, $consolidatedData, $explanations, $testName) {
    echo "--- {$testName} ---\n";
    
    // Input data
    echo "Input:\n";
    echo "- Date: " . $consolidatedData['date']->format('Y-m-d') . "\n";
    echo "- Checkin: " . ($consolidatedData['checkin'] ? $consolidatedData['checkin']->format('H:i:s') : 'null') . "\n";
    echo "- Checkout: " . ($consolidatedData['checkout'] ? $consolidatedData['checkout']->format('H:i:s') : 'null') . "\n";
    echo "- Checkout Source: " . $consolidatedData['checkout_source'] . "\n";
    echo "- Working Shift: " . $consolidatedData['working_shift']->name . " ({$consolidatedData['working_shift']->start_time} - {$consolidatedData['working_shift']->end_time})\n";
    echo "- Explanations: " . $explanations->count() . " records\n";
    
    // Check violations
    $result = $violationChecker->checkViolations($consolidatedData, $explanations);
    
    // Results
    echo "\nResults:\n";
    echo "✅ Has Violations: " . ($result['has_violations'] ? 'Yes' : 'No') . "\n";
    echo "✅ Late Minutes: " . $result['late_minutes'] . "\n";
    echo "✅ Early Minutes: " . $result['early_minutes'] . "\n";
    echo "✅ Missing Checkin: " . ($result['missing_checkin'] ? 'Yes' : 'No') . "\n";
    echo "✅ Missing Checkout: " . ($result['missing_checkout'] ? 'Yes' : 'No') . "\n";
    echo "✅ Status: " . $result['status'] . " (" . $result['status_text'] . ")\n";
    echo "✅ Status Color: " . $result['status_color'] . "\n";
    
    if (!empty($result['violations_summary'])) {
        echo "✅ Violations Summary:\n";
        foreach ($result['violations_summary'] as $violation) {
            echo "   - {$violation['message']} (Severity: {$violation['severity']})\n";
        }
    }
    
    echo "✅ Final Status: " . $result['final_status']['status'] . " (" . $result['final_status']['text'] . ")\n";
    echo "✅ Final Color: " . $result['final_status']['color'] . "\n";
    
    echo "\n";
    
    return $result;
}

// Run tests
foreach ($testCases as $testName => $testData) {
    $result = testViolationChecker(
        $violationChecker, 
        $testData['consolidated_data'], 
        $testData['explanations'], 
        $testName
    );
}

echo "=== EXPECTED RESULTS ===\n";
echo "Case 1: No violations, perfect status\n";
echo "Case 2: 30 minutes late, has_issue status\n";
echo "Case 3: 45 minutes early, has_issue status\n";
echo "Case 4: Using checkin as checkout, early departure detected\n";
echo "Case 5: Late but explained and approved, fully_explained status\n";

echo "\n=== SUMMARY ===\n";
echo "✅ AttendanceViolationChecker service created\n";
echo "✅ Separated violation checking logic from timesheet processing\n";
echo "✅ Supports consolidated data from AttendanceDataProcessor\n";
echo "✅ Handles explanations and final status determination\n";
echo "✅ Provides detailed violation summary\n";

?>
