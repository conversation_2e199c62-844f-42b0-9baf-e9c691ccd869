<?php

/**
 * Demo script để test logic lấy shifts với rules cho popup giải trình
 * 
 * Chạy: php demo_shift_rules_popup.php
 */

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== DEMO: SHIFTS WITH RULES FOR POPUP ===\n\n";

try {
    // Test với user 1814 và ngày 2025-06-01
    $userId = 1814;
    $date = '2025-06-01';
    
    echo "Testing với User ID: {$userId}, Date: {$date}\n\n";
    
    $user = App\Models\User::find($userId);
    if (!$user) {
        echo "❌ Không tìm thấy user {$userId}\n";
        exit;
    }
    
    echo "✅ User: {$user->name}\n\n";
    
    // Test method getAvailableShiftsWithRules
    echo "--- Test getAvailableShiftsWithRules() ---\n";
    $shiftsWithRules = $user->getAvailableShiftsWithRules($date);
    
    echo "Số lượng shifts: " . $shiftsWithRules->count() . "\n\n";
    
    foreach ($shiftsWithRules as $shift) {
        echo "🔧 Shift: {$shift->name} (ID: {$shift->id})\n";
        echo "   📅 Ngày: {$date}\n";
        echo "   ⏰ Thời gian hiện tại: {$shift->start_time} - {$shift->end_time}\n";
        echo "   ⏰ Thời gian gốc: {$shift->original_start_time} - {$shift->original_end_time}\n";
        echo "   📋 Có rule: " . ($shift->has_rule ? 'Có' : 'Không') . "\n";
        
        if ($shift->has_rule) {
            echo "   📝 Rule hiệu lực: {$shift->rule_effective_from} đến " . ($shift->rule_effective_to ?? 'không giới hạn') . "\n";
            echo "   🎯 Display: {$shift->name} ({$shift->start_time} - {$shift->end_time}) [Theo quy tắc mới]\n";
        } else {
            echo "   🎯 Display: {$shift->name} ({$shift->start_time} - {$shift->end_time})\n";
        }
        echo "\n";
    }
    
    // Test với ngày khác (không có rule)
    echo "--- Test với ngày khác (2025-05-01) ---\n";
    $shiftsWithRulesOld = $user->getAvailableShiftsWithRules('2025-05-01');
    
    foreach ($shiftsWithRulesOld as $shift) {
        echo "🔧 Shift: {$shift->name}\n";
        echo "   ⏰ Thời gian: {$shift->start_time} - {$shift->end_time}\n";
        echo "   📋 Có rule: " . ($shift->has_rule ? 'Có' : 'Không') . "\n";
        echo "\n";
    }
    
    // Test API response format
    echo "--- Test API Response Format ---\n";
    $formattedShifts = $shiftsWithRules->map(function ($shift) {
        return [
            'id' => $shift->id,
            'name' => $shift->name,
            'start_time' => $shift->start_time,
            'end_time' => $shift->end_time,
            'original_start_time' => $shift->original_start_time,
            'original_end_time' => $shift->original_end_time,
            'has_rule' => $shift->has_rule,
            'rule_effective_from' => $shift->rule_effective_from,
            'rule_effective_to' => $shift->rule_effective_to,
            'display_name' => $shift->name . ' (' . $shift->start_time . ' - ' . $shift->end_time . ')',
            'display_with_rule_info' => $shift->has_rule 
                ? $shift->name . ' (' . $shift->start_time . ' - ' . $shift->end_time . ') [Theo quy tắc mới]'
                : $shift->name . ' (' . $shift->start_time . ' - ' . $shift->end_time . ')',
        ];
    });
    
    echo "JSON Response:\n";
    echo json_encode([
        'success' => true,
        'data' => $formattedShifts,
        'date' => $date
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
    
    echo "=== TỔNG KẾT ===\n";
    echo "✅ Logic lấy shifts với rules đã hoạt động\n";
    echo "✅ Popup sẽ hiển thị thời gian từ shift_rule_histories\n";
    echo "✅ Có thể phân biệt ca có rule và ca không có rule\n";
    echo "✅ API endpoint đã sẵn sàng\n\n";
    
    echo "🎯 Kết quả mong đợi:\n";
    echo "- Popup giải trình sẽ hiển thị ca làm việc với thời gian từ shift_rule_histories\n";
    echo "- Có ghi chú [Theo quy tắc mới] cho ca có rule\n";
    echo "- JavaScript sẽ load động dựa trên ngày được chọn\n";
    
} catch (Exception $e) {
    echo "❌ Lỗi: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

?>
