<?php

/**
 * Debug script để test API shifts-with-rules
 * 
 * Chạy: php debug_shifts_api.php
 */

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== DEBUG: SHIFTS WITH RULES API ===\n\n";

try {
    // Test trực tiếp logic trong User model
    $userId = 1814;
    $date = '2025-06-01';
    
    echo "1. Testing User model logic...\n";
    $user = App\Models\User::find($userId);
    
    if (!$user) {
        echo "❌ User not found: {$userId}\n";
        exit;
    }
    
    echo "✅ User found: {$user->name}\n";
    
    // Test getAvailableShifts
    echo "\n2. Testing getAvailableShifts()...\n";
    $shifts = $user->getAvailableShifts();
    echo "Available shifts count: " . $shifts->count() . "\n";
    
    foreach ($shifts as $shift) {
        echo "  - {$shift->name} (ID: {$shift->id}): {$shift->start_time} - {$shift->end_time}\n";
    }
    
    // Test getAvailableShiftsWithRules
    echo "\n3. Testing getAvailableShiftsWithRules()...\n";
    $shiftsWithRules = $user->getAvailableShiftsWithRules($date);
    echo "Shifts with rules count: " . $shiftsWithRules->count() . "\n";
    
    foreach ($shiftsWithRules as $shift) {
        echo "  - {$shift->name} (ID: {$shift->id})\n";
        echo "    Current: {$shift->start_time} - {$shift->end_time}\n";
        echo "    Original: {$shift->original_start_time} - {$shift->original_end_time}\n";
        echo "    Has rule: " . ($shift->has_rule ? 'Yes' : 'No') . "\n";
        if ($shift->has_rule) {
            echo "    Rule period: {$shift->rule_effective_from} to " . ($shift->rule_effective_to ?? 'null') . "\n";
        }
        echo "\n";
    }
    
    // Test API controller logic
    echo "4. Testing API controller logic...\n";
    
    // Simulate request
    $request = new \Illuminate\Http\Request();
    $request->merge(['date' => $date]);
    
    // Mock auth user
    auth()->login($user);
    
    $controller = new \App\Http\Controllers\API\V1\AttendanceExplanationController();
    
    try {
        $response = $controller->getShiftsWithRules($request);
        $responseData = $response->getData(true);
        
        echo "API Response status: " . $response->getStatusCode() . "\n";
        echo "API Response success: " . ($responseData['success'] ? 'true' : 'false') . "\n";
        
        if (isset($responseData['data'])) {
            echo "API Response data count: " . count($responseData['data']) . "\n";
            
            foreach ($responseData['data'] as $shift) {
                echo "  - {$shift['name']}: {$shift['start_time']} - {$shift['end_time']}\n";
                echo "    Display: {$shift['display_with_rule_info']}\n";
            }
        }
        
        if (isset($responseData['message'])) {
            echo "API Response message: {$responseData['message']}\n";
        }
        
    } catch (\Exception $e) {
        echo "❌ API Controller error: " . $e->getMessage() . "\n";
        echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    }
    
    echo "\n=== SUMMARY ===\n";
    echo "✅ User model logic works\n";
    echo "✅ Shifts with rules logic works\n";
    echo "🔍 Check API authentication and route middleware\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

?>
