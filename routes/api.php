<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::as('api.')->prefix('v1')->group(function () {
    Route::as('passport.')
        ->group(function () {
            Route::post('login', [\App\Http\Controllers\API\V1\Auth\AccessTokenController::class, 'issueToken'])->name('token');
        });

    Route::middleware(['auth:api', 'auth.active'])->group(function () {
        Route::post('logout', [\App\Http\Controllers\API\V1\Auth\AuthController::class, 'logout']);
        Route::post('device', [\App\Http\Controllers\API\V1\Auth\AuthController::class, 'updateDevice']);
        Route::post('firebase-token', [\App\Http\Controllers\API\V1\Auth\AuthController::class, 'issueFirebaseToken']);

        Route::get('me', [\App\Http\Controllers\API\V1\Auth\ProfileController::class, 'show']);
        Route::put('me', [\App\Http\Controllers\API\V1\Auth\ProfileController::class, 'update']);
        Route::put('change-password', [\App\Http\Controllers\API\V1\Auth\ProfileController::class, 'changePassword']);

        Route::get('dashboard', [\App\Http\Controllers\API\V1\DashboardController::class, 'index']);

        // Users
        Route::get('users/all', [\App\Http\Controllers\API\V1\UserController::class, 'all']);
        Route::get('users/search', [\App\Http\Controllers\API\V1\UserController::class, 'search']);
        Route::get('users/{id}', [\App\Http\Controllers\API\V1\Auth\ProfileController::class, 'ofUser']);

        // File
        Route::post('file/upload', [\App\Http\Controllers\API\V1\File\FileController::class, 'upload']);

        // Bookings
        Route::resource('bookings', \App\Http\Controllers\API\V1\Booking\BookingController::class)->except('show', 'destroy');
        Route::get('bookings/filters', [\App\Http\Controllers\API\V1\Booking\BookingController::class, 'filters']);

        // Date Bookings
        Route::resource('date-bookings', \App\Http\Controllers\API\V1\Booking\BookingDateController::class)->only('index');
        Route::get('date-bookings/filters', [\App\Http\Controllers\API\V1\Booking\BookingDateController::class, 'filters']);

        // Lead report search.
        Route::prefix('lead-reports')->group(function () {
            Route::get('search/filters', [\App\Http\Controllers\API\V1\LeadReport\LeadReportStatisticController::class, 'getFilters']);
            Route::get('fast-filters', [\App\Http\Controllers\API\V1\LeadReport\FastFilterLeadReportController::class, 'index']);
            Route::get('search/get-doctors-by-marketing-teams', [\App\Http\Controllers\API\V1\LeadReport\LeadReportStatisticController::class, 'getDoctorsByMarketingTeams']);
            Route::get('search/get-lead-report-statistic', [\App\Http\Controllers\API\V1\LeadReport\LeadReportStatisticController::class, 'getLeadReportStatistic']);
        });

        // Rating report search.
        Route::prefix('rating-reports')->group(function () {
            Route::get('filters', [\App\Http\Controllers\API\V1\RatingReport\RatingReportController::class, 'getFilters']);
            Route::get('matrix', [\App\Http\Controllers\API\V1\RatingReport\RatingReportController::class, 'getMatrix']);
        });

        // User action logs.
        Route::resource('user-action-logs', \App\Http\Controllers\API\V1\UserActionLogController::class)
            ->only('store');

        // Notifications.
        Route::get('notifications/unread', [\App\Http\Controllers\API\V1\NotificationController::class, 'countUnread']);
        Route::get('notifications/latest', [\App\Http\Controllers\API\V1\NotificationController::class, 'getLatestUnreadNotification']);
        Route::resource('notifications', \App\Http\Controllers\API\V1\NotificationController::class)->only('index', 'show');

        // Departments
        Route::get('departments/by-type', [\App\Http\Controllers\API\V1\DepartmentController::class, 'getByType']);

        // Attendance Explanations
        Route::prefix('attendance-explanations')->group(function () {
            // Special routes first
            Route::get('calendar', [\App\Http\Controllers\API\V1\AttendanceExplanationController::class, 'getMonthlyCalendar']);
            Route::get('day/{date}', [\App\Http\Controllers\API\V1\AttendanceExplanationController::class, 'getDayAttendance']);
            Route::get('day/{date}/checkin-checkout-details', [\App\Http\Controllers\API\V1\AttendanceExplanationController::class, 'getDayCheckinCheckoutDetails']);
            Route::get('day/{date}/timesheet-validation', [\App\Http\Controllers\API\V1\AttendanceExplanationController::class, 'getTimesheetForShiftValidation']);
            Route::get('my-explanations', [\App\Http\Controllers\API\V1\AttendanceExplanationController::class, 'getMyExplanations']);
            Route::get('counts', [\App\Http\Controllers\API\V1\AttendanceExplanationController::class, 'getExplanationCounts']);
            Route::get('available-shifts', [\App\Http\Controllers\API\V1\AttendanceExplanationApprovalController::class, 'getAvailableShifts']);
            Route::get('shifts-with-rules', [\App\Http\Controllers\API\V1\AttendanceExplanationController::class, 'getShiftsWithRules']);

            // Action routes
            Route::post('{id}/pause', [\App\Http\Controllers\API\V1\AttendanceExplanationController::class, 'pause']);
            Route::post('{id}/resume', [\App\Http\Controllers\API\V1\AttendanceExplanationController::class, 'resume']);

            // CRUD routes
            Route::post('/', [\App\Http\Controllers\API\V1\AttendanceExplanationController::class, 'store']);
            Route::get('/{id}', [\App\Http\Controllers\API\V1\AttendanceExplanationController::class, 'show']);
            Route::put('/{id}', [\App\Http\Controllers\API\V1\AttendanceExplanationController::class, 'update']);
            Route::delete('/{id}', [\App\Http\Controllers\API\V1\AttendanceExplanationController::class, 'destroy']);
        });

        // Manager Approval Routes
        Route::prefix('manager/attendance-explanations')->group(function () {
            Route::get('pending', [\App\Http\Controllers\API\V1\AttendanceExplanationApprovalController::class, 'getManagerPendingExplanations']);
            Route::get('history', [\App\Http\Controllers\API\V1\AttendanceExplanationApprovalController::class, 'getManagerHistory']);
            Route::get('managed-users', [\App\Http\Controllers\API\V1\AttendanceExplanationApprovalController::class, 'getManagedUsers']);

            // Support both PATCH and PUT for approve
            Route::match(['patch', 'put'], '{id}/approve', [\App\Http\Controllers\API\V1\AttendanceExplanationApprovalController::class, 'managerApprove']);

            // Bulk approve/reject
            Route::post('bulk-approve', [\App\Http\Controllers\API\V1\AttendanceExplanationApprovalController::class, 'managerBulkApprove']);

            // User specific routes
            Route::get('user/{userId}/pending', [\App\Http\Controllers\API\V1\AttendanceExplanationApprovalController::class, 'getUserPendingExplanations']);
            Route::get('user/{userId}/approved', [\App\Http\Controllers\API\V1\AttendanceExplanationApprovalController::class, 'getUserApprovedExplanations']);
            Route::get('user/{userId}', [\App\Http\Controllers\API\V1\AttendanceExplanationApprovalController::class, 'getUserExplanations']); // Keep for backward compatibility
        });

        // HR Approval Routes
        Route::prefix('hr/attendance-explanations')->group(function () {
            Route::get('pending', [\App\Http\Controllers\API\V1\AttendanceExplanationApprovalController::class, 'getHrPendingExplanations']);
            Route::get('history', [\App\Http\Controllers\API\V1\AttendanceExplanationApprovalController::class, 'getHrHistory']);

            // Support both PATCH and PUT for approve
            Route::match(['patch', 'put'], '{id}/approve', [\App\Http\Controllers\API\V1\AttendanceExplanationApprovalController::class, 'hrApprove']);

            // Bulk approve/reject
            Route::post('bulk-approve', [\App\Http\Controllers\API\V1\AttendanceExplanationApprovalController::class, 'hrBulkApprove']);
        });
    });

    Route::middleware(['auth:api', 'auth.active'])->group(function () {
        Route::prefix('chat')->group(function () {
            Route::post('conversations/start', [\App\Http\Controllers\API\V1\Chat\ConversationController::class, 'startConversation']);
            Route::get('conversations/retrieve-direct', [\App\Http\Controllers\API\V1\Chat\ConversationController::class, 'retrieveDirectConversation']);
            Route::post('conversations/{conversation}/leave', [\App\Http\Controllers\API\V1\Chat\ConversationController::class, 'leaveConversation']);
            Route::post('conversations/{conversation}/user/add', [\App\Http\Controllers\API\V1\Chat\ConversationController::class, 'addUsersToConversation']);
            Route::post('conversations/{conversation}/user/remove', [\App\Http\Controllers\API\V1\Chat\ConversationController::class, 'removeUsersToConversation']);
            Route::put('conversations/{conversation}', [\App\Http\Controllers\API\V1\Chat\ConversationController::class, 'updateConversation']);
            Route::get('conversations/{conversation}/files/media', [\App\Http\Controllers\API\V1\Chat\ConversationController::class, 'getConversationMediaFiles']);
            Route::get('conversations/{conversation}/files/attachment', [\App\Http\Controllers\API\V1\Chat\ConversationController::class, 'getConversationAttachmentFiles']);
            Route::get('conversations/{conversation}/pinned-messages', [\App\Http\Controllers\API\V1\Chat\ConversationController::class, 'getConversationPinnedMessages']);
            Route::get('conversations/{conversation}/messages', [\App\Http\Controllers\API\V1\Chat\MessageController::class, 'searchConversationMessages']);
            Route::delete('conversations/{conversation}/disband', [\App\Http\Controllers\API\V1\Chat\ConversationController::class, 'disbandConversation']);
            Route::post('conversations/{conversation}/chat', [\App\Http\Controllers\API\V1\Chat\MessageController::class, 'chatToConversation']);
            Route::post('conversations/{conversation}/forward/{message}', [\App\Http\Controllers\API\V1\Chat\MessageController::class, 'forwardToConversation']);
            Route::post('conversations/{conversation}/toggle-pin', [\App\Http\Controllers\API\V1\Chat\ConversationController::class, 'togglePinConversation']);

            Route::get('messages', [\App\Http\Controllers\API\V1\Chat\MessageController::class, 'searchMessages']);
            Route::post('messages/{message}/reaction', [\App\Http\Controllers\API\V1\Chat\MessageController::class, 'reactionToMessage']);
            Route::get('messages/{message}/reactions', [\App\Http\Controllers\API\V1\Chat\MessageController::class, 'getMessageReactions']);
            Route::post('messages/{message}/unsent', [\App\Http\Controllers\API\V1\Chat\MessageController::class, 'unsentMessage']);
            Route::post('messages/{message}/pin', [\App\Http\Controllers\API\V1\Chat\MessageController::class, 'pinMessage']);
            Route::post('messages/{message}/unpin', [\App\Http\Controllers\API\V1\Chat\MessageController::class, 'unpinMessage']);

            Route::get('conversations/{conversation}/user/settings', [\App\Http\Controllers\API\V1\Chat\ParticipationController::class, 'getSettings']);
            Route::post('conversations/{conversation}/user/settings', [\App\Http\Controllers\API\V1\Chat\ParticipationController::class, 'applySettings']);
            Route::post('conversations/{conversation}/user/{user}/assign-admin', [\App\Http\Controllers\API\V1\Chat\ParticipationController::class, 'assignAdmin']);
            Route::post('conversations/{conversation}/user/{user}/remove-admin', [\App\Http\Controllers\API\V1\Chat\ParticipationController::class, 'removeAdmin']);
        });
    });

    Route::middleware(['auth:api', 'auth.active'])->group(function () {
        Route::prefix('timesheets')->group(function () {
            Route::get('/', [\App\Http\Controllers\API\V1\Timesheet\TimesheetController::class, 'index']);
            Route::get('in-month', [\App\Http\Controllers\API\V1\Timesheet\TimesheetController::class, 'getListInMonth']);

            Route::get('shifts', [\App\Http\Controllers\API\V1\Timesheet\TimesheetController::class, 'getAvailableShifts']);
            Route::get('status', [\App\Http\Controllers\API\V1\Timesheet\TimesheetController::class, 'getCheckingStatus']);

            Route::post('checkin', [\App\Http\Controllers\API\V1\Timesheet\TimesheetController::class, 'checkin'])->middleware('auth.device_code');
            Route::post('checkout', [\App\Http\Controllers\API\V1\Timesheet\TimesheetController::class, 'checkout'])->middleware('auth.device_code');
        });

        Route::resource('company-addresses', \App\Http\Controllers\API\V1\CompanyAddressController::class)->only('index');
    });
});
