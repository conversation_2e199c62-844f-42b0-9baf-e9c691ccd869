<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Shift Determination for Workday Calculation
    |--------------------------------------------------------------------------
    |
    | Khi enabled, hệ thống sẽ sử dụng logic mới để xác định ca làm việc:
    | 1. Ưu tiên ca checkout (nếu có)
    | 2. Fallback ca checkin (nếu không có checkout)
    |
    | Khi disabled, sử dụng ca được lưu trong timesheet (logic cũ)
    |
    */
    'use_shift_determination' => env('TIMESHEET_USE_SHIFT_DETERMINATION', false),

    /*
    |--------------------------------------------------------------------------
    | Simple Workday Calculator for Multiple Timesheets
    |--------------------------------------------------------------------------
    |
    | Khi enabled, sử dụng logic đơn giản cho multiple timesheets:
    | 1. Checkin sớm nhất → Checkout/Checkin muộn nhất
    | 2. Nếu bản ghi cuối chỉ có checkin → dùng checkin đó làm "checkout"
    |
    */
    'use_simple_calculator' => env('TIMESHEET_USE_SIMPLE_CALCULATOR', false),

    /*
    |--------------------------------------------------------------------------
    | Shift Determination Settings
    |--------------------------------------------------------------------------
    |
    | Các cài đặt cho logic xác định ca làm việc
    |
    */
    'shift_determination' => [
        // Khoảng cách tối đa (phút) để coi là "gần" với ca làm việc
        'max_distance_minutes' => 120,
        
        // Có ưu tiên ca có thời gian gần nhất không
        'prefer_closest_shift' => true,
        
        // Có log quá trình xác định ca không (để debug)
        'enable_logging' => env('TIMESHEET_SHIFT_DETERMINATION_LOG', false),
    ],
];
